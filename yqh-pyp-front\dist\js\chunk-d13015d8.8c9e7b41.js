(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-d13015d8"],{"5af8":function(t,e,i){"use strict";i.r(e);i("7f7f");var a=function(){var t=this,e=t._self._c;return e("div",[t._m(0),e("van-list",{attrs:{finished:t.finished,"finished-text":"没有更多了"},on:{load:t.onLoad},model:{value:t.loading,callback:function(e){t.loading=e},expression:"loading"}},[e("div",{staticClass:"data"},[e("van-collapse",{model:{value:t.schedulesActive,callback:function(e){t.schedulesActive=e},expression:"schedulesActive"}},t._l(t.dataList,(function(i){return e("van-collapse-item",{key:i.id,staticStyle:{"margin-bottom":"10px"},attrs:{name:i.id}},[e("van-card",{staticStyle:{background:"white"},attrs:{slot:"title"},slot:"title",scopedSlots:t._u([{key:"tags",fn:function(){return[e("div",{staticStyle:{display:"flex","justify-content":"space-between",width:"110%"}},[e("van-tag",{staticStyle:{margin:"5px 10px 5px 0px"},attrs:{size:"medium",plain:"",type:t._f("statusTypeFilter")(i.status)}},[t._v(t._s(t._f("statusFilter")(i.status)))]),e("div",{staticStyle:{display:"flex"}},[0==i.status?e("van-button",{staticStyle:{width:"60px"},attrs:{size:"small",round:"",block:"",type:"primary"},on:{click:function(e){return t.$router.push({name:"hotelSuccess",query:{orderId:i.id,id:t.activityId}})}}},[t._v("去支付")]):t._e(),0==i.status||1==i.status?e("van-button",{staticStyle:{width:"60px","margin-left":"10px"},attrs:{size:"small",round:"",block:"",type:"danger"},on:{click:function(e){return t.cancelOrder(i.id)}}},[t._v("取消")]):t._e()],1)],1)]},proxy:!0}],null,!0)},[e("div",{staticStyle:{"font-size":"18px"},attrs:{slot:"title"},slot:"title"},[t._v(t._s(i.name))]),e("div",{staticStyle:{"padding-top":"10px","font-size":"14px",color:"grey"},attrs:{slot:"desc"},slot:"desc"},[e("div",[t._v("\n                订单号(后四位)："+t._s(i.orderSn.substring(i.orderSn.length-4,i.orderSn.length))+"\n              ")]),e("div",[t._v("订单金额：￥"+t._s(i.totalAmount)+"元")]),e("div",[t._v("创建时间："+t._s(i.createOn))])])]),e("van-steps",{attrs:{active:"-1",direction:"vertical"}},t._l(i.hotelOrderDetailEntities,(function(i){return e("van-step",{key:i.id},[e("div",[e("div",[t._v("\n                  酒店名称："+t._s(i.hotelName?i.hotelName:"未知")+"\n                ")]),e("div",[t._v("房型名称："+t._s(i.roomName))]),e("div",[t._v("数量："+t._s(i.number))]),e("div",[t._v("\n                  入住时间： "+t._s(i.inDate.substring(0,10))+" ~\n                  "+t._s(i.outDate.substring(5,10))+"\n                ")]),e("div",[t._v("价格：￥"+t._s(i.price)+"元")])])])})),1)],1)})),1)],1)])],1)},s=[function(){var t=this,e=t._self._c;return e("div",{staticClass:"nav-title",staticStyle:{"margin-top":"8px"}},[e("div",{staticClass:"color"}),e("div",{staticClass:"text"},[t._v("酒店订单")])])}],n=(i("ac6a"),i("e210")),l={data:function(){return{activityId:void 0,loading:!1,finished:!1,flag:!1,dataList:[],pageIndex:1,pageSize:10,totalPage:0,placeActive:0,orderStatus:n,schedulesActive:[]}},filters:{statusFilter:function(t){var e=n.filter((function(e){return e.key===t}));if(e.length>=1)return e[0].value},statusTypeFilter:function(t){var e=n.filter((function(e){return e.key===t}));if(e.length>=1)return e[0].type}},mounted:function(){document.title="酒店订单列表",this.activityId=this.$route.query.id},methods:{onSearch:function(){this.pageIndex=1,this.dataList=[],this.getActivityList()},onLoad:function(){this.flag||this.getActivityList()},getActivityList:function(){var t=this;this.flag=!0,this.$fly.get("/pyp/web/hotel/hotelorder/list",{page:this.pageIndex,limit:this.pageSize,activityId:this.activityId}).then((function(e){t.loading=!1,200==e.code?(t.flag=!1,e.page.list&&e.page.list.length>0?(e.page.list.forEach((function(e){t.dataList.push(e),t.schedulesActive.push(e.id)})),t.totalPage=e.page.totalPage,t.pageIndex++,t.loading=!1,t.totalPage<t.pageIndex?t.finished=!0:t.finished=!1):t.finished=!0):(vant.Toast(e.msg),t.dataList=[],t.totalPage=0,t.finished=!0)}))},cancelOrder:function(t){var e=this;vant.Dialog.confirm({title:"提示",message:"确认取消订单?"}).then((function(){e.$fly.get("/pyp/web/hotel/hotelorder/cancel",{orderId:t}).then((function(t){t&&200===t.code?(vant.Toast("取消成功"),e.onSearch()):vant.Toast(t.msg)}))})).catch((function(){}))}}},o=l,r=(i("ce74"),i("2877")),c=Object(r["a"])(o,a,s,!1,null,"51291434",null);e["default"]=c.exports},ac6a:function(t,e,i){for(var a=i("cadf"),s=i("0d58"),n=i("2aba"),l=i("7726"),o=i("32e9"),r=i("84f2"),c=i("2b4c"),d=c("iterator"),u=c("toStringTag"),v=r.Array,f={CSSRuleList:!0,CSSStyleDeclaration:!1,CSSValueList:!1,ClientRectList:!1,DOMRectList:!1,DOMStringList:!1,DOMTokenList:!0,DataTransferItemList:!1,FileList:!1,HTMLAllCollection:!1,HTMLCollection:!1,HTMLFormElement:!1,HTMLSelectElement:!1,MediaList:!0,MimeTypeArray:!1,NamedNodeMap:!1,NodeList:!0,PaintRequestList:!1,Plugin:!1,PluginArray:!1,SVGLengthList:!1,SVGNumberList:!1,SVGPathSegList:!1,SVGPointList:!1,SVGStringList:!1,SVGTransformList:!1,SourceBufferList:!1,StyleSheetList:!0,TextTrackCueList:!1,TextTrackList:!1,TouchList:!1},p=s(f),g=0;g<p.length;g++){var h,y=p[g],S=f[y],m=l[y],L=m&&m.prototype;if(L&&(L[d]||o(L,d,v),L[u]||o(L,u,y),r[y]=v,S))for(h in a)L[h]||n(L,h,a[h],!0)}},ae2e:function(t,e,i){},ce74:function(t,e,i){"use strict";i("ae2e")},e210:function(t){t.exports=JSON.parse('[{"key":0,"value":"待付款","type":"primary"},{"key":1,"value":"已付款","type":"success"},{"key":2,"value":"已取消"},{"key":3,"value":"退款中","type":"warning"},{"key":4,"value":"退款成功"},{"key":5,"value":"退款失败","type":"danger"}]')}}]);
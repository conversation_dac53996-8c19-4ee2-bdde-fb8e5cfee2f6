(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-b023bb82"],{"23e6":function(a,t,e){"use strict";e.r(t);e("7f7f");var n=function(){var a=this,t=a._self._c;return t("div",{staticClass:"recharge-payment"},[t("div",{staticClass:"order-info"},[t("h3",[a._v(a._s(a.orderTitle))]),t("div",{staticClass:"order-details"},[t("div",{staticClass:"detail-item"},[t("span",{staticClass:"label"},[a._v("套餐名称：")]),t("span",{staticClass:"value"},[a._v(a._s(a.orderInfo.packageName))])]),t("div",{staticClass:"detail-item"},[t("span",{staticClass:"label"},[a._v(a._s("activity"===a.orderType?"可创建活动：":"充值次数："))]),t("span",{staticClass:"value"},[a._v(a._s(a.orderInfo.countValue)+a._s("activity"===a.orderType?"个":"次"))])]),t("div",{staticClass:"detail-item"},[t("span",{staticClass:"label"},[a._v("订单金额：")]),t("span",{staticClass:"value price"},[a._v("¥"+a._s(a.orderInfo.amount))])]),t("div",{staticClass:"detail-item"},[t("span",{staticClass:"label"},[a._v("订单编号：")]),t("span",{staticClass:"value"},[a._v(a._s(a.orderInfo.orderSn))])])])]),t("div",{staticClass:"payment-methods"},[t("h4",[a._v("选择支付方式")]),t("van-radio-group",{model:{value:a.selectedPayment,callback:function(t){a.selectedPayment=t},expression:"selectedPayment"}},[t("van-cell-group",[t("van-cell",{attrs:{clickable:""},on:{click:function(t){a.selectedPayment="wechat"}},scopedSlots:a._u([{key:"title",fn:function(){return[t("div",{staticClass:"payment-option"},[t("img",{staticClass:"payment-icon",attrs:{src:"http://yqhpyp.oss-cn-shanghai.aliyuncs.com/20250627/e7171ae049dd4c7dacb4020ea0aef415.png",alt:"微信支付"}}),t("span",[a._v("微信支付")])])]},proxy:!0},{key:"right-icon",fn:function(){return[t("van-radio",{attrs:{name:"wechat"}})]},proxy:!0}])})],1)],1)],1),t("div",{staticClass:"payment-actions"},[t("van-button",{attrs:{type:"primary",size:"large",loading:a.paying},on:{click:a.submitPayment}},[a._v("\n      立即支付 ¥"+a._s(a.orderInfo.amount)+"\n    ")])],1),a.salesmanInfo?t("div",{staticClass:"salesman-info"},[t("h4",[a._v("推荐业务员")]),t("div",{staticClass:"salesman-card"},[t("div",{staticClass:"avatar"},[t("img",{attrs:{src:a.salesmanInfo.avatar||"../../assets/mine.png",alt:"业务员头像"}})]),t("div",{staticClass:"info"},[t("div",{staticClass:"name"},[a._v(a._s(a.salesmanInfo.name||a.salesmanInfo.realName))]),t("div",{staticClass:"contact"},[a._v(a._s(a.salesmanInfo.mobile||a.salesmanInfo.phone))]),a.salesmanInfo.company?t("div",{staticClass:"company"},[a._v(a._s(a.salesmanInfo.company))]):a._e()])])]):a._e()])},s=[],o=(e("a481"),e("386d"),{name:"RechargePayment",data:function(){return{orderInfo:{packageName:"",countValue:0,amount:0,orderSn:""},salesmanInfo:null,selectedPayment:"wechat",paying:!1,orderType:"recharge"}},computed:{orderTitle:function(){return"activity"===this.orderType?"创建活动订单":"充值订单"}},mounted:function(){this.rebuildUrl(),this.loadOrderInfo()},methods:{loadOrderInfo:function(){var a=this,t=this.$route.query.orderId,e=this.$route.query.from,n=this.$route.query.type;if(this.orderType=n||"recharge",t){var s="/pyp/web/salesman/getRechargeOrderInfo";"account"===e&&(s="/pyp/web/account/getOrderInfo"),this.$fly.get(s,{orderId:t}).then((function(t){var n,s;200===t.code||200===t.code?("account"===e?(n=t.orderInfo||t.data,s=t.packageInfo||t.data):(n=t.orderInfo,s=t.packageInfo),a.orderInfo={packageName:s.name||s.packageName,countValue:s.countValue,amount:n.payAmount||n.amount||n.totalAmount,orderSn:n.orderSn}):a.$toast(t.msg||"加载订单信息失败")})).catch((function(t){console.error("加载订单信息失败:",t),a.$toast("加载订单信息失败")}));var o=this.$route.query.salesmanId;o&&this.loadSalesmanInfo(o)}else this.$toast("订单参数错误")},loadSalesmanInfo:function(a){var t=this;this.$fly.get("/pyp/web/salesman/info/".concat(a)).then((function(a){200===a.code?t.salesmanInfo=a.result||a.salesman:console.error("加载业务员信息失败:",a.msg)})).catch((function(a){console.error("加载业务员信息失败:",a)}))},submitPayment:function(){if(this.selectedPayment){this.paying=!0;var a=this.$route.query.orderId;"wechat"===this.selectedPayment?this.payWithWechat(a):"alipay"===this.selectedPayment&&this.payWithAlipay(a)}else this.$toast("请选择支付方式")},payWithWechat:function(a){var t=this;this.$fly.get("/pyp/web/activity/recharge/pay/wechat",{orderSn:this.orderInfo.orderSn}).then((function(e){200===e.code?"undefined"!==typeof WeixinJSBridge?WeixinJSBridge.invoke("getBrandWCPayRequest",{appId:e.result.appId,timeStamp:e.result.timeStamp,nonceStr:e.result.nonceStr,package:e.result.packageValue,signType:e.result.signType,paySign:e.result.paySign},(function(e){"get_brand_wcpay_request:ok"===e.err_msg?(t.$toast.success("支付成功"),t.handlePaymentSuccess(a)):t.$toast("支付失败"),t.paying=!1})):(t.$toast("请在微信中打开"),t.paying=!1):(t.$toast(e.msg||"支付失败"),t.paying=!1)})).catch((function(a){console.error("微信支付失败:",a),t.$toast("支付失败"),t.paying=!1}))},payWithAlipay:function(a){var t=this;this.$fly.get("/pyp/web/activity/recharge/pay/alipay",{orderSn:this.orderInfo.orderSn}).then((function(a){200===a.code?t.$router.push({name:"commonAlipay",query:{form:encodeURIComponent(a.result)}}):t.$toast(a.msg||"支付失败"),t.paying=!1})).catch((function(a){console.error("支付宝支付失败:",a),t.$toast("支付失败"),t.paying=!1}))},rebuildUrl:function(){var a=window.location,t=a.href,e=a.protocol,n=a.host,s=a.pathname,o=a.search,r=a.hash;console.log(window.location),o=o||"?";var i="".concat(e,"//").concat(n).concat(s).concat(o).concat(r);console.log(i),i!==t&&window.location.replace(i)},handlePaymentSuccess:function(a){var t=this.$route.query.from,e=this.$route.query.salesmanId;if("account"===t)this.$router.push({name:"account",query:{activityId:this.$route.query.activityId,refresh:"true"}});else{var n={orderId:a,type:this.orderType,from:t};e&&(n.salesmanId=e),this.$router.push({name:"paymentSuccess",query:n})}}}}),r=o,i=(e("c90f"),e("2877")),c=Object(i["a"])(r,n,s,!1,null,"93e6acda",null);t["default"]=c.exports},"386d":function(a,t,e){"use strict";var n=e("cb7c"),s=e("83a1"),o=e("5f1b");e("214f")("search",1,(function(a,t,e,r){return[function(e){var n=a(this),s=void 0==e?void 0:e[t];return void 0!==s?s.call(e,n):new RegExp(e)[t](String(n))},function(a){var t=r(e,a,this);if(t.done)return t.value;var i=n(a),c=String(this),l=i.lastIndex;s(l,0)||(i.lastIndex=0);var d=o(i,c);return s(i.lastIndex,l)||(i.lastIndex=l),null===d?-1:d.index}]}))},"83a1":function(a,t){a.exports=Object.is||function(a,t){return a===t?0!==a||1/a===1/t:a!=a&&t!=t}},c25b:function(a,t,e){},c90f:function(a,t,e){"use strict";e("c25b")}}]);
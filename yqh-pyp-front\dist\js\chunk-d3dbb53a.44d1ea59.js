(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-d3dbb53a","chunk-37a545c8"],{"1b69":function(t,i,e){"use strict";e.r(i);e("7f7f");var n,a=function(){var t=this,i=t._self._c;return i("div",{staticStyle:{background:"white","margin-bottom":"20px"}},[i("van-row",{attrs:{gutter:"20"}},[i("van-col",{attrs:{span:"16"}},[i("van-swipe",{staticStyle:{width:"100%"},attrs:{autoplay:3e3}},t._l(t.activityInfo.appFileList,(function(t,e){return i("van-swipe-item",{key:e},[i("van-image",{attrs:{width:"100%",src:t.url}})],1)})),1)],1),i("van-col",{attrs:{span:"8"}},[i("div",{staticStyle:{"margin-top":"20px"}},[i("van-cell",{attrs:{title:"会议名称",value:t.activityInfo.name}}),t.activityInfo.address?i("van-cell",{attrs:{title:"会议地点",value:t.activityInfo.address}}):t._e(),i("van-cell",{attrs:{title:"会议开始时间",value:t.activityInfo.startTime}}),i("van-cell",{attrs:{title:"会议结束时间",value:t.activityInfo.endTime}}),i("van-cell",{attrs:{title:"登录信息"},scopedSlots:t._u([{key:"default",fn:function(){return[t.userInfo?i("van-button",{attrs:{type:"primary",size:"small",round:""}},[t._v(t._s(t.userInfo.username)+"("+t._s(t.userInfo.mobile)+")")]):i("van-button",{attrs:{type:"info",size:"small",round:""},on:{click:t.showLogin}},[t._v("未登录(点击登录)")])]},proxy:!0}])}),"1736999159118508033"!=t.activityId?i("van-cell",{attrs:{title:"报名状态"},scopedSlots:t._u([{key:"default",fn:function(){return[1==t.isPay?i("van-button",{attrs:{type:"primary",size:"small",round:""},on:{click:t.turnApply}},[t._v("已报名")]):2==t.isPay?i("van-button",{attrs:{type:"warning",size:"small",round:""},on:{click:t.turnApply}},[t._v("已报名未交费")]):i("van-button",{attrs:{type:"info",size:"small",round:""},on:{click:t.turnApply}},[t._v("未报名")])]},proxy:!0}],null,!1,567203517)}):t._e()],1)])],1),i("van-tabs",{staticClass:"nav",on:{click:t.onClick},model:{value:t.cmsId,callback:function(i){t.cmsId=i},expression:"cmsId"}},t._l(t.cmsList,(function(t){return i("van-tab",{key:t.id,attrs:{title:t.title,name:t.id}})})),1),i("pclogin")],1)},o=[],s=e("ade3"),c=(e("a481"),e("6762"),e("2fdb"),e("cacf")),r=e("7dcb"),l=function(){var t=this,i=t._self._c;return i("van-dialog",{attrs:{title:"登录操作",width:"400px"},on:{confirm:t.login},model:{value:t.showPcLogin,callback:function(i){t.showPcLogin=i},expression:"showPcLogin"}},[i("div",{staticClass:"text-center padding"},[i("van-cell-group",{attrs:{inset:""}},[i("van-field",{attrs:{name:"手机号",label:"手机号",required:"",placeholder:"手机号",rules:[{required:!0,message:"请填写手机号"}]},model:{value:t.mobile,callback:function(i){t.mobile=i},expression:"mobile"}}),"1736999159118508033"!=t.activityId?i("van-field",{attrs:{center:"",clearable:"",maxlength:"6",label:"短信验证码",required:"",placeholder:"请输入短信验证码"},scopedSlots:t._u([{key:"button",fn:function(){return[i("van-button",{attrs:{size:"small",type:"primary",disabled:t.waiting},on:{click:function(i){return t.doSendSmsCode()}}},[t.waiting?i("span",[t._v(t._s(t.waitingTime)+"秒后重发")]):i("span",{staticStyle:{"font-size":"13px"}},[t._v("获取验证码")])])]},proxy:!0}],null,!1,850170227),model:{value:t.code,callback:function(i){t.code=i},expression:"code"}}):t._e()],1)],1)])},u=[],d={name:"pclogin",computed:{showPcLogin:{get:function(){return this.$store.state.user.showPcLogin},set:function(t){this.$store.commit("user/changePcLogin",t)}}},data:function(){return{activityId:void 0,waiting:!1,waitingTime:60,mobile:"",code:""}},mounted:function(){this.activityId=this.$route.query.id},methods:{login:function(){var t=this;return this.mobile?Object(c["b"])(this.mobile)?this.code||0x181b0f883e693000==this.activityId?/^\d{6}$/.test(this.code)||0x181b0f883e693000==this.activityId?void this.$fly.post("/pyp/web/user/pcLogin",{mobile:this.mobile,code:0x181b0f883e693000==this.activityId?"121212":this.code}).then((function(i){i&&200===i.code?(vant.Toast("登录成功"),t.$store.commit("user/update",i.userInfo),t.showPcLogin=!1,"applyIndex"!=t.$route.name&&"livesDetail"!=t.$route.name||location.reload()):vant.Toast(i.msg)})):(vant.Toast("验证码错误"),!1):(vant.Toast("请输入验证码"),!1):(vant.Toast("手机号格式错误"),!1):(vant.Toast("请输入手机号"),!1)},doSendSmsCode:function(){var t=this;return this.mobile?Object(c["b"])(this.mobile)?void this.$fly.post("/pyp/sms/sms/send",{mobile:this.mobile,activityId:this.activityId}).then((function(i){i&&200===i.code?(t.countdown(),vant.Toast("发送验证码成功")):vant.Toast(i.msg)})):(vant.Toast("手机号格式错误"),!1):(vant.Toast("请输入手机号"),!1)},countdown:function(){var t=this;this.waiting=!0;var i=window.setInterval((function(){t.waitingTime--,t.waitingTime<0&&(window.clearInterval(i),t.waitingTime=60,t.waiting=!1)}),1e3)}}},f=d,p=e("2877"),y=Object(p["a"])(f,l,u,!1,null,null,null),m=y.exports,h={components:{pclogin:m},data:function(){return{activityId:void 0,bannerIndex:0,cmsId:void 0,cmsList:[],cmsInfo:{},activityInfo:{}}},computed:{userInfo:{get:function(){return this.$store.state.user.userInfo},set:function(t){this.$store.commit("user/update",t)}},isPay:{get:function(){return this.$store.state.apply.isPay},set:function(t){this.$store.commit("apply/update",t)}}},mounted:function(){var t=(new Date).getTime();localStorage.setItem("logAddTime",t+36e5),this.openid=this.$cookie.get("openid"),this.activityId=this.$route.query.id,this.cmsId=sessionStorage.getItem("cmsId")||this.$route.query.cmsId,this.checkLogin(),this.getActivityInfo(),this.activityLogCount()},methods:(n={showLogin:function(){this.$store.commit("user/changePcLogin",!0)},checkLogin:function(){var t=this;this.$fly.get("/pyp/web/user/checkLogin").then((function(i){t.userInfo=i.result,t.userInfo?t.checkApply():t.isPay=0}))},bannerIndexChange:function(t){this.bannerIndex=t},checkApply:function(){var t=this;this.$fly.get("/pyp/web/activity/activityuserapplyorder/checkApply",{activityId:this.activityId}).then((function(i){200==i.code?(t.isPay=i.isPay,1==t.isPay||2==t.isPay&&vant.Dialog.alert({title:"提示",message:"您有一笔注册费待支付，点击跳转"}).then((function(){t.$router.push({name:"applySuccess",query:{orderId:i.result,id:t.activityId}})}))):vant.Toast(i.msg)}))},changeShowType:function(t){this.$emit("changeShowType",t)},turnApply:function(){var t=this.cmsList.filter((function(t){return t.model&&t.model.includes("applyIndex")}));if(t){var i=t[0];sessionStorage.setItem("cmsId",i.id);var e=i.model.replace("${activityId}",i.activityId);this.$router.push(JSON.parse(e))}else vant.Toast("暂未开启报名")},getActivityInfo:function(){var t=this;this.$fly.get("/pyp/activity/activity/info/".concat(this.activityId)).then((function(i){if(t.loading=!1,200==i.code){t.activityInfo=i.activity,document.title=t.activityInfo.name;var e=t.activityInfo.startTime,n=new Date(e.replace(/-/g,"/")),a=new Date,o=n.getTime()-a.getTime();t.dateCompare=o>0?o:0,t.getCmsList()}}))},activityLogCount:function(){var t=localStorage.getItem("logAddTime"),i=(new Date).getTime();i>t&&this.$fly.post("/pyp/activity/activityviewlog/count",{activityId:this.activityId,device:r["a"].getVersion()}).then((function(t){}))},getCmsList:function(){var t=this;this.$fly.get("/pyp/cms/cms/findByActivityId/".concat(this.activityId)).then((function(i){t.loading=!1,200==i.code?(t.cmsList=i.result,t.cmsId?t.changeShowType(t.cmsId):t.changeShowType(t.cmsList[0].id)):(vant.Toast(i.msg),t.cmsList=[])}))},onClick:function(t){if(console.log(t),sessionStorage.setItem("cmsId",t),this.cmsInfo=this.cmsList.filter((function(i){return i.id==t}))[0],this.cmsInfo.url&&Object(c["d"])(this.cmsInfo.url))location.href=this.cmsInfo.url;else if(this.cmsInfo.model&&this.isJSON(this.cmsInfo.model)){var i=this.cmsInfo.model.replace("${activityId}",this.cmsInfo.activityId);this.$router.push(JSON.parse(i))}else"cmsIndex"==this.$route.name?this.changeShowType(t):this.$router.push({name:"cmsIndex",query:{id:this.activityId,cmsId:t}})}},Object(s["a"])(n,"bannerIndexChange",(function(t){this.bannerIndex=t})),Object(s["a"])(n,"isJSON",(function(t){try{return JSON.parse(t),!0}catch(i){return!1}})),n)},v=h,g=(e("dd7a"),Object(p["a"])(v,a,o,!1,null,"7bd3d808",null));i["default"]=g.exports},"386d":function(t,i,e){"use strict";var n=e("cb7c"),a=e("83a1"),o=e("5f1b");e("214f")("search",1,(function(t,i,e,s){return[function(e){var n=t(this),a=void 0==e?void 0:e[i];return void 0!==a?a.call(e,n):new RegExp(e)[i](String(n))},function(t){var i=s(e,t,this);if(i.done)return i.value;var c=n(t),r=String(this),l=c.lastIndex;a(l,0)||(c.lastIndex=0);var u=o(c,r);return a(c.lastIndex,l)||(c.lastIndex=l),null===u?-1:u.index}]}))},"4b5a":function(t,i,e){},"6a76":function(t,i,e){"use strict";e("4b5a")},"7dcb":function(t,i,e){"use strict";e("a481"),e("4917");i["a"]={getVersion:function(){var t=navigator.userAgent||window.navigator.userAgent,i=/HUAWEI|HONOR/gi,e=/[^;]+(?= Build)/gi,n=/CPU iPhone OS \d[_\d]*/gi,a=/CPU OS \d[_\d]*/gi,o=/Windows NT \d[\.\d]*/gi,s=/Linux x\d[_\d]*/gi,c=/Mac OS X \d[_|\.\d]*/gi;return/Android/gi.test(t)?i.test(t)?t.match(i)[0]+t.match(e)[0]:t.match(e)[0]:/iPhone/gi.test(t)?t.match(n)[0].replace(/CPU iPhone OS/gi,"iPhone").replace(/_/g,"."):/iPad/gi.test(t)?t.match(a)[0].replace(/CPU OS/gi,"iPad").replace(/_/g,"."):/Windows/gi.test(t)?"Windows "+(Math.min(parseInt(t.match(o)[0].replace(/Windows NT /,""))+1,10)<7?"XP":Math.min(parseInt(t.match(o)[0].replace(/Windows NT /,""))+1,10)):/Linux/gi.test(t)?t.match(s)[0]:/Macintosh/gi.test(t)?t.match(c)[0].replace(/_/g,"."):"unknown"}}},"83a1":function(t,i){t.exports=Object.is||function(t,i){return t===i?0!==t||1/t===1/i:t!=t&&i!=i}},ab3e:function(t,i,e){"use strict";e.r(i);var n=function(){var t=this,i=t._self._c;return i("div",{class:t.isMobilePhone?"page":"page pc-container"},[t.isMobilePhone?t._e():i("pcheader"),t.isMobilePhone?i("van-swipe",{attrs:{autoplay:3e3}},t._l(t.activityInfo.appFileList,(function(t,e){return i("van-swipe-item",{key:e},[i("van-image",{attrs:{width:"100%",src:t.url}})],1)})),1):t._e(),i("van-empty",[t.channelInfo&&t.channelInfo.isVerify?i("img",{attrs:{slot:"image",src:0==t.orderInfo.verifyStatus?"http://mpjoy.oss-cn-beijing.aliyuncs.com/********/168906ff7dc74239835061f0a8cfe5fd.png":1==t.orderInfo.verifyStatus?"http://mpjoy.oss-cn-beijing.aliyuncs.com/20210618/7361a2571f8a41809960a577bb123733.png":"http://mpjoy.oss-cn-beijing.aliyuncs.com/20211228/13da716239e94e0fa7f84712e06ce9d1.png",alt:""},slot:"image"}):i("img",{attrs:{slot:"image",src:0==t.orderInfo.status?"http://mpjoy.oss-cn-beijing.aliyuncs.com/********/168906ff7dc74239835061f0a8cfe5fd.png":1==t.orderInfo.status?"http://mpjoy.oss-cn-beijing.aliyuncs.com/20210618/7361a2571f8a41809960a577bb123733.png":"http://mpjoy.oss-cn-beijing.aliyuncs.com/20211228/13da716239e94e0fa7f84712e06ce9d1.png",alt:""},slot:"image"}),t.channelInfo&&t.channelInfo.isVerify?i("div",{attrs:{slot:"description"},slot:"description"},[t._v(t._s(t.verifyText))]):i("div",{attrs:{slot:"description"},slot:"description"},[t._v(t._s(78==t.activityId&&"待付款"==t.statusText?"您有一笔注册费待支付":0x173d6b4cea590000==t.activityId&&"已付款"==t.statusText?"已报名":t.statusText))]),i("div",{staticClass:"orderInfo"},[i("div",{staticStyle:{color:"#969799","font-size":"16px","padding-bottom":"20px","text-align":"center"}},[t._v("\n        报名费用：￥"+t._s(t.orderInfo.totalAmount)+"元\n      ")])]),0x173d6b4cea590000!=t.activityId?i("div",{staticClass:"botton"},[i("div",{staticClass:"button-item"},[i("img",{staticClass:"button-image",attrs:{src:"http://mpjoy.oss-cn-beijing.aliyuncs.com/********/191a0fb893ae4ab8af27f20d0f395b7d.png",alt:""},on:{click:function(i){return t.$router.push({name:"applyProxyList",query:{id:t.orderInfo.activityId}})}}})]),1==t.orderInfo.status&&t.activityInfo.applySuccessLive?i("div",{staticClass:"button-item"},[i("img",{staticClass:"button-image",attrs:{src:135==t.activityId?"http://mpjoy.oss-cn-beijing.aliyuncs.com/********/d94288caf6594b0296b02486deca7e24.png":"http://mpjoy.oss-cn-beijing.aliyuncs.com/********/d47c65f2457f46e39bc70b1db37e0c39.png",alt:""},on:{click:t.turnLive}})]):t._e(),2!=t.orderInfo.status?i("div",{staticClass:"button-item"},[i("img",{staticClass:"button-image",attrs:{src:"http://mpjoy.oss-cn-beijing.aliyuncs.com/********/7b6f394d16be435b963ae7b2101207a5.png",alt:""},on:{click:t.cancelApply}})]):t._e(),t.channelInfo&&1==t.channelInfo.isBankTransfer&&0==t.orderInfo.status?i("div",{staticClass:"button-item"},[i("img",{staticClass:"button-image",attrs:{src:"http://mpjoy.oss-cn-beijing.aliyuncs.com/********/7af84898829c490881f7baea837a9c01.png",alt:""},on:{click:t.bankTransfer}})]):t._e(),t.channelInfo&&1==t.channelInfo.isWechatPay&&0==t.orderInfo.status?i("div",{staticClass:"button-item"},[i("img",{staticClass:"button-image",attrs:{src:"http://mpjoy.oss-cn-beijing.aliyuncs.com/********/1d27eefbace94b3c9b993312f66b5501.png",alt:""},on:{click:t.weixin}})]):t._e(),0x16eba6c96f403000!=t.activityId?i("div",{staticClass:"button-item"},[i("img",{staticClass:"button-image",attrs:{src:"http://mpjoy.oss-cn-beijing.aliyuncs.com/********/f11ec2b762c84ad6a6212e20d7fbb056.png",alt:""},on:{click:function(i){return t.$router.push({name:"cmsIndex",query:{id:t.orderInfo.activityId}})}}})]):t._e(),i("div",{staticClass:"button-item"},[i("img",{staticClass:"button-image",attrs:{src:"http://mpjoy.oss-cn-beijing.aliyuncs.com/********/31f3f3299ff24260854da46383278ca7.png",alt:""},on:{click:function(i){return t.$router.push({name:"meMine",query:{id:t.orderInfo.activityId}})}}})]),t.activityInfo.applyProxy?i("div",{staticClass:"button-item"},[i("img",{staticClass:"button-image",attrs:{src:"http://mpjoy.oss-cn-beijing.aliyuncs.com/20230705/22399e1748d9412498d282a4204be741.png",alt:""},on:{click:function(i){return t.$router.push({name:"applyProxyList",query:{id:t.orderInfo.activityId}})}}})]):t._e()]):t._e()]),i("follow-modal",{attrs:{show:t.showFollowMOdal,activityId:t.activityId,appid:t.appid,qrcodeImgUrl:t.activityInfo.subscribeImg},on:{close:function(i){t.showFollowMOdal=!1}}}),t.activityInfo.applyYhy?i("div",{staticClass:"bottomdiy"},[i("a",{staticClass:"item",attrs:{href:"https://zhaoshengniuren.com/mp_yqh/#/yunhuiyi"}},[t._v("技术支持：云会易")])]):t._e()],1)},a=[],o=(e("386d"),e("a481"),e("cacf")),s=e("1b69"),c={components:{FollowModal:function(){return e.e("chunk-2d0d3132").then(e.bind(null,"5ae5"))},pcheader:s["default"]},data:function(){return{isMobilePhone:Object(o["c"])(),orderId:void 0,activityId:void 0,appid:void 0,activityInfo:{},channelInfo:{},orderInfo:{},showFollowMOdal:!1,verifyText:"",statusText:""}},computed:{isPay:{get:function(){return this.$store.state.apply.isPay},set:function(t){this.$store.commit("apply/update",t)}}},mounted:function(){document.title="报名成功",this.appid=this.$cookie.get("appid"),this.orderId=this.$route.query.orderId,this.activityId=this.$route.query.id,this.$route.query.token&&this.$cookie.set("token",this.$route.query.token),this.rebuildUrl(),this.getOrderInfo()},methods:{getActivityInfo:function(t){var i=this;this.$fly.get("/pyp/activity/activity/info/".concat(t)).then((function(t){i.loading=!1,200==t.code?(i.activityInfo=t.activity,i.activityInfo.subscribeImg&&1==i.orderInfo.status&&(i.showFollowMOdal=!0)):(vant.Toast(t.msg),i.activityInfo={})}))},getOrderInfo:function(){var t=this;this.$fly.get("/pyp/web/activity/activityuserapplyorder/infoProxy/".concat(this.orderId)).then((function(i){if(t.loading=!1,200==i.code){t.orderInfo=i.result,t.statusText=i.statusText,t.verifyText=i.verifyText;var e=0;0==t.orderInfo.status&&(e=2),1==t.orderInfo.status&&(e=1),t.isPay=e,t.getActivityInfo(t.orderInfo.activityId),null!=t.orderInfo.applyActivityChannelConfigId&&t.getApplyActivityChannelConfig(t.orderInfo.applyActivityChannelConfigId)}else vant.Toast(i.msg),t.orderInfo={},t.statusText=""}))},getApplyActivityChannelConfig:function(t){var i=this;this.$fly.get("/pyp/web/apply/applyactivitychannelconfig/info/".concat(t)).then((function(t){200==t.code&&(i.channelInfo=t.applyActivityChannelConfig,0==i.orderInfo.status&&i.channelInfo.bankTransferNotify&&vant.Dialog.confirm({title:"付款须知",message:i.channelInfo.bankTransferNotify,confirmButtonText:"取消",cancelButtonText:"已缴费(忽略)"}).then((function(){})).catch((function(){})))}))},cancelApply:function(){var t=this;vant.Dialog.confirm({title:"提示",message:"确认取消报名?"}).then((function(){t.$fly.post("/pyp/web/activity/activityuserapplyorder/cancelOrder",{id:t.orderInfo.activityUserId}).then((function(i){i&&200===i.code?(vant.Toast("取消成功"),t.$store.commit("apply/update",0),t.isMobilePhone?t.$router.replace({name:"meMine",query:{id:t.orderInfo.activityId}}):t.$router.go(-1)):vant.Toast(i.msg)}))})).catch((function(){}))},bankTransfer:function(){vant.Dialog.confirm({title:"付款须知",message:this.channelInfo.bankTransferNotify,confirmButtonText:"取消",cancelButtonText:"已缴费(忽略)"}).then((function(){})).catch((function(){}))},weixin:function(){var t=this;this.$fly.get("/pyp/web/activity/activityuserapplyorder/payProxy",{orderId:t.orderId}).then((function(t){t&&200===t.code?WeixinJSBridge.invoke("getBrandWCPayRequest",{appId:t.result.appId,timeStamp:t.result.timeStamp,nonceStr:t.result.nonceStr,package:t.result.packageValue,signType:t.result.signType,paySign:t.result.paySign},(function(t){console.log("开始支付"),location.reload()})):vant.Toast(t.msg)}))},ali:function(){var t=this;this.$fly.get("/pyp/web/activity/activityuserapplyorder/payAliProxy",{orderId:this.orderId}).then((function(i){i&&200===i.code?t.$router.push({name:"commonAlipay",query:{form:encodeURIComponent(i.result)}}):vant.Toast(i.msg)}))},back:function(){var t=sessionStorage.getItem("returnUrl");t?location.href=decodeURIComponent(t):this.$router.push({name:"cmsIndex",query:{id:this.activityId}})},turnLive:function(){78==this.activityId&&1==this.orderInfo.status?location.href="https://wx.vzan.com/live/mk/aggspread/618781199/f9dfa6f6-5cbf-11ed-93c5-043f72d45e40?v=1667621522612":this.$router.push({name:"livesIndex",query:{id:this.activityId}})},turnExam:function(){this.$router.push({name:"examIndex",query:{id:this.activityId}})},rebuildUrl:function(){var t=window.location,i=t.href,e=t.protocol,n=t.host,a=t.pathname,o=t.search,s=t.hash;console.log(window.location),o=o||"?";var c="".concat(e,"//").concat(n).concat(a).concat(o).concat(s);console.log(c),c!==i&&window.location.replace(c)}}},r=c,l=(e("6a76"),e("2877")),u=Object(l["a"])(r,n,a,!1,null,"17548ca4",null);i["default"]=u.exports},ade3:function(t,i,e){"use strict";e.d(i,"a",(function(){return s}));var n=e("53ca");function a(t,i){if("object"!==Object(n["a"])(t)||null===t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var a=e.call(t,i||"default");if("object"!==Object(n["a"])(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===i?String:Number)(t)}function o(t){var i=a(t,"string");return"symbol"===Object(n["a"])(i)?i:String(i)}function s(t,i,e){return i=o(i),i in t?Object.defineProperty(t,i,{value:e,enumerable:!0,configurable:!0,writable:!0}):t[i]=e,t}},cad8:function(t,i,e){},dd7a:function(t,i,e){"use strict";e("cad8")}}]);
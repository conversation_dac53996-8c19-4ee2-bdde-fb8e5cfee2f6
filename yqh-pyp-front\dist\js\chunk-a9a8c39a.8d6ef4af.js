(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-a9a8c39a"],{"2e08":function(t,a,n){var s=n("9def"),e=n("9744"),c=n("be13");t.exports=function(t,a,n,i){var o=String(c(t)),l=o.length,r=void 0===n?" ":String(n),v=s(a);if(v<=l||""==r)return o;var d=v-l,m=e.call(r,Math.ceil(d/r.length));return m.length>d&&(m=m.slice(0,d)),i?m+o:o+m}},"4bc3":function(t,a,n){"use strict";n.r(a);var s=function(){var t=this,a=t._self._c;return a("div",{staticClass:"contact-salesman-page"},[a("div",{staticClass:"header"},[a("van-nav-bar",{attrs:{title:"联系业务员","left-text":"返回","left-arrow":""},on:{"click-left":function(a){return t.$router.go(-1)}}})],1),a("div",{staticClass:"content"},[t.loading?a("div",{staticClass:"loading-state"},[a("van-loading",{attrs:{type:"spinner",color:"#1989fa"}},[t._v("加载中...")])],1):a("div",[t.salesmanInfo?a("div",{staticClass:"salesman-card"},[a("div",{staticClass:"salesman-header"},[a("div",{staticClass:"avatar"},[a("van-image",{attrs:{src:t.salesmanAvatar,fit:"cover",round:"",width:"80",height:"80"},scopedSlots:t._u([{key:"error",fn:function(){return[a("van-icon",{attrs:{name:"user-o",size:"40"}})]},proxy:!0}],null,!1,2435912096)})],1),a("div",{staticClass:"info"},[a("div",{staticClass:"name"},[t._v(t._s(t.salesmanInfo.salesmanName))]),a("div",{staticClass:"code"},[t._v("编号："+t._s(t.salesmanInfo.salesmanCode))]),a("div",{staticClass:"binding-time"},[t._v("\n              绑定时间："+t._s(t.formatDate(t.salesmanInfo.bindingTime))+"\n            ")])]),a("div",{staticClass:"status"},[a("van-tag",{attrs:{type:"success",size:"medium"}},[t._v("专属业务员")])],1)]),a("div",{staticClass:"contact-list"},[t.salesmanInfo.salesmanMobile?a("div",{staticClass:"contact-item",on:{click:t.makeCall}},[a("div",{staticClass:"contact-icon"},[a("van-icon",{attrs:{name:"phone-o",size:"24",color:"#1989fa"}})],1),a("div",{staticClass:"contact-content"},[a("div",{staticClass:"contact-title"},[t._v("电话咨询")]),a("div",{staticClass:"contact-desc"},[t._v(t._s(t.salesmanInfo.salesmanMobile))]),a("div",{staticClass:"contact-tips"},[t._v("工作时间：9:00-18:00")])]),a("div",{staticClass:"contact-action"},[a("van-button",{attrs:{type:"primary",size:"small"}},[t._v("拨打")])],1)]):t._e(),a("div",{staticClass:"contact-item",on:{click:t.contactWechat}},[a("div",{staticClass:"contact-icon"},[a("van-icon",{attrs:{name:"chat-o",size:"24",color:"#07c160"}})],1),t._m(0),a("div",{staticClass:"contact-action"},[a("van-button",{attrs:{type:"success",size:"small"}},[t._v("联系")])],1)]),a("div",{staticClass:"contact-item",on:{click:t.openOnlineService}},[a("div",{staticClass:"contact-icon"},[a("van-icon",{attrs:{name:"service-o",size:"24",color:"#ff976a"}})],1),t._m(1),a("div",{staticClass:"contact-action"},[a("van-button",{attrs:{type:"warning",size:"small"}},[t._v("咨询")])],1)]),a("div",{staticClass:"contact-item",on:{click:t.sendEmail}},[a("div",{staticClass:"contact-icon"},[a("van-icon",{attrs:{name:"envelop-o",size:"24",color:"#646566"}})],1),t._m(2),a("div",{staticClass:"contact-action"},[a("van-button",{attrs:{plain:"",size:"small"}},[t._v("发送")])],1)])]),a("div",{staticClass:"service-time"},[a("div",{staticClass:"time-title"},[t._v("服务时间")]),a("div",{staticClass:"time-list"},[a("div",{staticClass:"time-item"},[a("van-icon",{attrs:{name:"clock-o"}}),a("span",[t._v("工作日：9:00 - 18:00")])],1),a("div",{staticClass:"time-item"},[a("van-icon",{attrs:{name:"clock-o"}}),a("span",[t._v("周末：10:00 - 17:00")])],1),a("div",{staticClass:"time-item"},[a("van-icon",{attrs:{name:"chat-o"}}),a("span",[t._v("微信咨询：24小时在线")])],1)])]),a("div",{staticClass:"faq-section"},[a("div",{staticClass:"faq-title"},[t._v("常见问题")]),a("van-collapse",{model:{value:t.activeNames,callback:function(a){t.activeNames=a},expression:"activeNames"}},[a("van-collapse-item",{attrs:{title:"如何更换业务员？",name:"1"}},[a("div",{staticClass:"faq-content"},[t._v('\n                您可以在"我的专属业务员"页面点击"解除绑定"，然后重新绑定新的业务员。\n              ')])]),a("van-collapse-item",{attrs:{title:"业务员服务范围？",name:"2"}},[a("div",{staticClass:"faq-content"},[t._v("\n                专属业务员为您提供产品咨询、技术支持、优惠活动通知、定制化解决方案等全方位服务。\n              ")])]),a("van-collapse-item",{attrs:{title:"联系不上业务员怎么办？",name:"3"}},[a("div",{staticClass:"faq-content"},[t._v("\n                如果无法联系到您的专属业务员，请拨打客服热线400-123-4567，我们会为您安排其他业务员协助。\n              ")])])],1)],1)]):a("div",{staticClass:"no-salesman"},[a("van-empty",{attrs:{image:"https://img.yzcdn.cn/vant/custom-empty-image.png",description:"您还没有绑定专属业务员"}},[a("van-button",{attrs:{type:"primary"},on:{click:t.goToBind}},[t._v("立即绑定")])],1)],1)])]),a("van-action-sheet",{attrs:{actions:t.contactActions,"cancel-text":"取消"},on:{select:t.onContactSelect},model:{value:t.contactSheetVisible,callback:function(a){t.contactSheetVisible=a},expression:"contactSheetVisible"}})],1)},e=[function(){var t=this,a=t._self._c;return a("div",{staticClass:"contact-content"},[a("div",{staticClass:"contact-title"},[t._v("微信咨询")]),a("div",{staticClass:"contact-desc"},[t._v("添加微信好友")]),a("div",{staticClass:"contact-tips"},[t._v("24小时在线服务")])])},function(){var t=this,a=t._self._c;return a("div",{staticClass:"contact-content"},[a("div",{staticClass:"contact-title"},[t._v("在线客服")]),a("div",{staticClass:"contact-desc"},[t._v("即时在线咨询")]),a("div",{staticClass:"contact-tips"},[t._v("快速响应，专业解答")])])},function(){var t=this,a=t._self._c;return a("div",{staticClass:"contact-content"},[a("div",{staticClass:"contact-title"},[t._v("邮件咨询")]),a("div",{staticClass:"contact-desc"},[t._v("发送邮件详细咨询")]),a("div",{staticClass:"contact-tips"},[t._v("适合复杂问题咨询")])])}],c=(n("f576"),n("96cf"),n("1da1")),i={name:"ContactSalesman",data:function(){return{loading:!0,salesmanInfo:null,activeNames:[],contactSheetVisible:!1,contactActions:[{name:"复制微信号",value:"copy"},{name:"添加微信好友",value:"add"},{name:"发送名片",value:"card"}]}},computed:{salesmanAvatar:function(){return"https://img.yzcdn.cn/vant/cat.jpeg"}},mounted:function(){this.getSalesmanContact()},methods:{getSalesmanContact:function(){var t=Object(c["a"])(regeneratorRuntime.mark((function t(){var a;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,this.loading=!0,t.next=4,this.$fly.get("/pyp/web/salesman/binding/getSalesmanContact");case 4:a=t.sent,200===a.code&&(this.salesmanInfo=a),t.next=11;break;case 8:t.prev=8,t.t0=t["catch"](0),t.t0.response&&500===t.t0.response.data.code?this.salesmanInfo=null:this.$toast("获取联系信息失败");case 11:return t.prev=11,this.loading=!1,t.finish(11);case 14:case"end":return t.stop()}}),t,this,[[0,8,11,14]])})));function a(){return t.apply(this,arguments)}return a}(),makeCall:function(){var t=this;this.salesmanInfo&&this.salesmanInfo.salesmanMobile&&this.$dialog.confirm({title:"拨打电话",message:"确定要拨打 ".concat(this.salesmanInfo.salesmanMobile," 吗？"),confirmButtonText:"拨打",cancelButtonText:"取消"}).then((function(){window.location.href="tel:".concat(t.salesmanInfo.salesmanMobile)}))},contactWechat:function(){this.contactSheetVisible=!0},onContactSelect:function(t){switch(t.value){case"copy":this.copyWechatId();break;case"add":this.addWechatFriend();break;case"card":this.sendBusinessCard();break}},copyWechatId:function(){var t="salesman_wechat_id";this.copyToClipboard(t),this.$toast.success("微信号已复制")},addWechatFriend:function(){this.$toast("请手动搜索微信号添加好友")},sendBusinessCard:function(){this.$toast("名片发送功能开发中")},copyToClipboard:function(t){if(navigator.clipboard)navigator.clipboard.writeText(t);else{var a=document.createElement("textarea");a.value=t,document.body.appendChild(a),a.select(),document.execCommand("copy"),document.body.removeChild(a)}},openOnlineService:function(){this.$toast("正在连接在线客服...")},sendEmail:function(){var t="<EMAIL>",a="咨询问题",n="您好，我是您的客户，有问题需要咨询。\n\n我的业务员：".concat(this.salesmanInfo.salesmanName,"\n业务员编号：").concat(this.salesmanInfo.salesmanCode,"\n\n问题描述：\n");window.location.href="mailto:".concat(t,"?subject=").concat(encodeURIComponent(a),"&body=").concat(encodeURIComponent(n))},goToBind:function(){this.$router.push("/salesman/my-salesman")},formatDate:function(t){if(!t)return"";var a=new Date(t);return"".concat(a.getFullYear(),"-").concat(String(a.getMonth()+1).padStart(2,"0"),"-").concat(String(a.getDate()).padStart(2,"0"))}}},o=i,l=(n("cd06"),n("2877")),r=Object(l["a"])(o,s,e,!1,null,"fbe65f50",null);a["default"]=r.exports},"59d1":function(t,a,n){},9744:function(t,a,n){"use strict";var s=n("4588"),e=n("be13");t.exports=function(t){var a=String(e(this)),n="",c=s(t);if(c<0||c==1/0)throw RangeError("Count can't be negative");for(;c>0;(c>>>=1)&&(a+=a))1&c&&(n+=a);return n}},cd06:function(t,a,n){"use strict";n("59d1")},f576:function(t,a,n){"use strict";var s=n("5ca1"),e=n("2e08"),c=n("a25f"),i=/Version\/10\.\d+(\.\d+)?( Mobile\/\w+)? Safari\//.test(c);s(s.P+s.F*i,"String",{padStart:function(t){return e(this,t,arguments.length>1?arguments[1]:void 0,!0)}})}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-e2050b20","chunk-37a545c8"],{"1b69":function(t,e,i){"use strict";i.r(e);i("7f7f");var n,a=function(){var t=this,e=t._self._c;return e("div",{staticStyle:{background:"white","margin-bottom":"20px"}},[e("van-row",{attrs:{gutter:"20"}},[e("van-col",{attrs:{span:"16"}},[e("van-swipe",{staticStyle:{width:"100%"},attrs:{autoplay:3e3}},t._l(t.activityInfo.appFileList,(function(t,i){return e("van-swipe-item",{key:i},[e("van-image",{attrs:{width:"100%",src:t.url}})],1)})),1)],1),e("van-col",{attrs:{span:"8"}},[e("div",{staticStyle:{"margin-top":"20px"}},[e("van-cell",{attrs:{title:"会议名称",value:t.activityInfo.name}}),t.activityInfo.address?e("van-cell",{attrs:{title:"会议地点",value:t.activityInfo.address}}):t._e(),e("van-cell",{attrs:{title:"会议开始时间",value:t.activityInfo.startTime}}),e("van-cell",{attrs:{title:"会议结束时间",value:t.activityInfo.endTime}}),e("van-cell",{attrs:{title:"登录信息"},scopedSlots:t._u([{key:"default",fn:function(){return[t.userInfo?e("van-button",{attrs:{type:"primary",size:"small",round:""}},[t._v(t._s(t.userInfo.username)+"("+t._s(t.userInfo.mobile)+")")]):e("van-button",{attrs:{type:"info",size:"small",round:""},on:{click:t.showLogin}},[t._v("未登录(点击登录)")])]},proxy:!0}])}),"1736999159118508033"!=t.activityId?e("van-cell",{attrs:{title:"报名状态"},scopedSlots:t._u([{key:"default",fn:function(){return[1==t.isPay?e("van-button",{attrs:{type:"primary",size:"small",round:""},on:{click:t.turnApply}},[t._v("已报名")]):2==t.isPay?e("van-button",{attrs:{type:"warning",size:"small",round:""},on:{click:t.turnApply}},[t._v("已报名未交费")]):e("van-button",{attrs:{type:"info",size:"small",round:""},on:{click:t.turnApply}},[t._v("未报名")])]},proxy:!0}],null,!1,567203517)}):t._e()],1)])],1),e("van-tabs",{staticClass:"nav",on:{click:t.onClick},model:{value:t.cmsId,callback:function(e){t.cmsId=e},expression:"cmsId"}},t._l(t.cmsList,(function(t){return e("van-tab",{key:t.id,attrs:{title:t.title,name:t.id}})})),1),e("pclogin")],1)},s=[],o=i("ade3"),r=(i("a481"),i("6762"),i("2fdb"),i("cacf")),c=i("7dcb"),l=function(){var t=this,e=t._self._c;return e("van-dialog",{attrs:{title:"登录操作",width:"400px"},on:{confirm:t.login},model:{value:t.showPcLogin,callback:function(e){t.showPcLogin=e},expression:"showPcLogin"}},[e("div",{staticClass:"text-center padding"},[e("van-cell-group",{attrs:{inset:""}},[e("van-field",{attrs:{name:"手机号",label:"手机号",required:"",placeholder:"手机号",rules:[{required:!0,message:"请填写手机号"}]},model:{value:t.mobile,callback:function(e){t.mobile=e},expression:"mobile"}}),"1736999159118508033"!=t.activityId?e("van-field",{attrs:{center:"",clearable:"",maxlength:"6",label:"短信验证码",required:"",placeholder:"请输入短信验证码"},scopedSlots:t._u([{key:"button",fn:function(){return[e("van-button",{attrs:{size:"small",type:"primary",disabled:t.waiting},on:{click:function(e){return t.doSendSmsCode()}}},[t.waiting?e("span",[t._v(t._s(t.waitingTime)+"秒后重发")]):e("span",{staticStyle:{"font-size":"13px"}},[t._v("获取验证码")])])]},proxy:!0}],null,!1,850170227),model:{value:t.code,callback:function(e){t.code=e},expression:"code"}}):t._e()],1)],1)])},u=[],d={name:"pclogin",computed:{showPcLogin:{get:function(){return this.$store.state.user.showPcLogin},set:function(t){this.$store.commit("user/changePcLogin",t)}}},data:function(){return{activityId:void 0,waiting:!1,waitingTime:60,mobile:"",code:""}},mounted:function(){this.activityId=this.$route.query.id},methods:{login:function(){var t=this;return this.mobile?Object(r["b"])(this.mobile)?this.code||0x181b0f883e693000==this.activityId?/^\d{6}$/.test(this.code)||0x181b0f883e693000==this.activityId?void this.$fly.post("/pyp/web/user/pcLogin",{mobile:this.mobile,code:0x181b0f883e693000==this.activityId?"121212":this.code}).then((function(e){e&&200===e.code?(vant.Toast("登录成功"),t.$store.commit("user/update",e.userInfo),t.showPcLogin=!1,"applyIndex"!=t.$route.name&&"livesDetail"!=t.$route.name||location.reload()):vant.Toast(e.msg)})):(vant.Toast("验证码错误"),!1):(vant.Toast("请输入验证码"),!1):(vant.Toast("手机号格式错误"),!1):(vant.Toast("请输入手机号"),!1)},doSendSmsCode:function(){var t=this;return this.mobile?Object(r["b"])(this.mobile)?void this.$fly.post("/pyp/sms/sms/send",{mobile:this.mobile,activityId:this.activityId}).then((function(e){e&&200===e.code?(t.countdown(),vant.Toast("发送验证码成功")):vant.Toast(e.msg)})):(vant.Toast("手机号格式错误"),!1):(vant.Toast("请输入手机号"),!1)},countdown:function(){var t=this;this.waiting=!0;var e=window.setInterval((function(){t.waitingTime--,t.waitingTime<0&&(window.clearInterval(e),t.waitingTime=60,t.waiting=!1)}),1e3)}}},m=d,f=i("2877"),p=Object(f["a"])(m,l,u,!1,null,null,null),v=p.exports,g={components:{pclogin:v},data:function(){return{activityId:void 0,bannerIndex:0,cmsId:void 0,cmsList:[],cmsInfo:{},activityInfo:{}}},computed:{userInfo:{get:function(){return this.$store.state.user.userInfo},set:function(t){this.$store.commit("user/update",t)}},isPay:{get:function(){return this.$store.state.apply.isPay},set:function(t){this.$store.commit("apply/update",t)}}},mounted:function(){var t=(new Date).getTime();localStorage.setItem("logAddTime",t+36e5),this.openid=this.$cookie.get("openid"),this.activityId=this.$route.query.id,this.cmsId=sessionStorage.getItem("cmsId")||this.$route.query.cmsId,this.checkLogin(),this.getActivityInfo(),this.activityLogCount()},methods:(n={showLogin:function(){this.$store.commit("user/changePcLogin",!0)},checkLogin:function(){var t=this;this.$fly.get("/pyp/web/user/checkLogin").then((function(e){t.userInfo=e.result,t.userInfo?t.checkApply():t.isPay=0}))},bannerIndexChange:function(t){this.bannerIndex=t},checkApply:function(){var t=this;this.$fly.get("/pyp/web/activity/activityuserapplyorder/checkApply",{activityId:this.activityId}).then((function(e){200==e.code?(t.isPay=e.isPay,1==t.isPay||2==t.isPay&&vant.Dialog.alert({title:"提示",message:"您有一笔注册费待支付，点击跳转"}).then((function(){t.$router.push({name:"applySuccess",query:{orderId:e.result,id:t.activityId}})}))):vant.Toast(e.msg)}))},changeShowType:function(t){this.$emit("changeShowType",t)},turnApply:function(){var t=this.cmsList.filter((function(t){return t.model&&t.model.includes("applyIndex")}));if(t){var e=t[0];sessionStorage.setItem("cmsId",e.id);var i=e.model.replace("${activityId}",e.activityId);this.$router.push(JSON.parse(i))}else vant.Toast("暂未开启报名")},getActivityInfo:function(){var t=this;this.$fly.get("/pyp/activity/activity/info/".concat(this.activityId)).then((function(e){if(t.loading=!1,200==e.code){t.activityInfo=e.activity,document.title=t.activityInfo.name;var i=t.activityInfo.startTime,n=new Date(i.replace(/-/g,"/")),a=new Date,s=n.getTime()-a.getTime();t.dateCompare=s>0?s:0,t.getCmsList()}}))},activityLogCount:function(){var t=localStorage.getItem("logAddTime"),e=(new Date).getTime();e>t&&this.$fly.post("/pyp/activity/activityviewlog/count",{activityId:this.activityId,device:c["a"].getVersion()}).then((function(t){}))},getCmsList:function(){var t=this;this.$fly.get("/pyp/cms/cms/findByActivityId/".concat(this.activityId)).then((function(e){t.loading=!1,200==e.code?(t.cmsList=e.result,t.cmsId?t.changeShowType(t.cmsId):t.changeShowType(t.cmsList[0].id)):(vant.Toast(e.msg),t.cmsList=[])}))},onClick:function(t){if(console.log(t),sessionStorage.setItem("cmsId",t),this.cmsInfo=this.cmsList.filter((function(e){return e.id==t}))[0],this.cmsInfo.url&&Object(r["d"])(this.cmsInfo.url))location.href=this.cmsInfo.url;else if(this.cmsInfo.model&&this.isJSON(this.cmsInfo.model)){var e=this.cmsInfo.model.replace("${activityId}",this.cmsInfo.activityId);this.$router.push(JSON.parse(e))}else"cmsIndex"==this.$route.name?this.changeShowType(t):this.$router.push({name:"cmsIndex",query:{id:this.activityId,cmsId:t}})}},Object(o["a"])(n,"bannerIndexChange",(function(t){this.bannerIndex=t})),Object(o["a"])(n,"isJSON",(function(t){try{return JSON.parse(t),!0}catch(e){return!1}})),n)},h=g,y=(i("dd7a"),Object(f["a"])(h,a,s,!1,null,"7bd3d808",null));e["default"]=y.exports},"28a5":function(t,e,i){"use strict";var n=i("aae3"),a=i("cb7c"),s=i("ebd6"),o=i("0390"),r=i("9def"),c=i("5f1b"),l=i("520a"),u=i("79e5"),d=Math.min,m=[].push,f="split",p="length",v="lastIndex",g=4294967295,h=!u((function(){RegExp(g,"y")}));i("214f")("split",2,(function(t,e,i,u){var y;return y="c"=="abbc"[f](/(b)*/)[1]||4!="test"[f](/(?:)/,-1)[p]||2!="ab"[f](/(?:ab)*/)[p]||4!="."[f](/(.?)(.?)/)[p]||"."[f](/()()/)[p]>1||""[f](/.?/)[p]?function(t,e){var a=String(this);if(void 0===t&&0===e)return[];if(!n(t))return i.call(a,t,e);var s,o,r,c=[],u=(t.ignoreCase?"i":"")+(t.multiline?"m":"")+(t.unicode?"u":"")+(t.sticky?"y":""),d=0,f=void 0===e?g:e>>>0,h=new RegExp(t.source,u+"g");while(s=l.call(h,a)){if(o=h[v],o>d&&(c.push(a.slice(d,s.index)),s[p]>1&&s.index<a[p]&&m.apply(c,s.slice(1)),r=s[0][p],d=o,c[p]>=f))break;h[v]===s.index&&h[v]++}return d===a[p]?!r&&h.test("")||c.push(""):c.push(a.slice(d)),c[p]>f?c.slice(0,f):c}:"0"[f](void 0,0)[p]?function(t,e){return void 0===t&&0===e?[]:i.call(this,t,e)}:i,[function(i,n){var a=t(this),s=void 0==i?void 0:i[e];return void 0!==s?s.call(i,a,n):y.call(String(a),i,n)},function(t,e){var n=u(y,t,this,e,y!==i);if(n.done)return n.value;var l=a(t),m=String(this),f=s(l,RegExp),p=l.unicode,v=(l.ignoreCase?"i":"")+(l.multiline?"m":"")+(l.unicode?"u":"")+(h?"y":"g"),b=new f(h?l:"^(?:"+l.source+")",v),S=void 0===e?g:e>>>0;if(0===S)return[];if(0===m.length)return null===c(b,m)?[m]:[];var _=0,I=0,x=[];while(I<m.length){b.lastIndex=h?I:0;var k,w=c(b,h?m:m.slice(I));if(null===w||(k=d(r(b.lastIndex+(h?0:I)),m.length))===_)I=o(m,I,p);else{if(x.push(m.slice(_,I)),x.length===S)return x;for(var T=1;T<=w.length-1;T++)if(x.push(w[T]),x.length===S)return x;I=_=k}}return x.push(m.slice(_)),x}]}))},"66c7":function(t,e,i){"use strict";i("4917"),i("a481");var n=/([yMdhsm])(\1*)/g,a="yyyy-MM-dd";function s(t,e){e-=(t+"").length;for(var i=0;i<e;i++)t="0"+t;return t}e["a"]={formatDate:{format:function(t,e){return e=e||a,e.replace(n,(function(e){switch(e.charAt(0)){case"y":return s(t.getFullYear(),e.length);case"M":return s(t.getMonth()+1,e.length);case"d":return s(t.getDate(),e.length);case"w":return t.getDay()+1;case"h":return s(t.getHours(),e.length);case"m":return s(t.getMinutes(),e.length);case"s":return s(t.getSeconds(),e.length)}}))},parse:function(t,e){var i=e.match(n),a=t.match(/(\d)+/g);if(i.length==a.length){for(var s=new Date(1970,0,1),o=0;o<i.length;o++){var r=parseInt(a[o]),c=i[o];switch(c.charAt(0)){case"y":s.setFullYear(r);break;case"M":s.setMonth(r-1);break;case"d":s.setDate(r);break;case"h":s.setHours(r);break;case"m":s.setMinutes(r);break;case"s":s.setSeconds(r);break}}return s}return null},toWeek:function(t){var e=new Date(t).getDay(),i="";switch(e){case 0:i="s";break;case 1:i="m";break;case 2:i="t";break;case 3:i="w";break;case 4:i="t";break;case 5:i="f";break;case 6:i="s";break}return i}},toUserLook:function(t){var e=Math.floor(t/3600%24),i=Math.floor(t/60%60);return e<1?i+"分":e+"时"+i+"分"}}},"6c0c":function(t,e,i){},"7dcb":function(t,e,i){"use strict";i("a481"),i("4917");e["a"]={getVersion:function(){var t=navigator.userAgent||window.navigator.userAgent,e=/HUAWEI|HONOR/gi,i=/[^;]+(?= Build)/gi,n=/CPU iPhone OS \d[_\d]*/gi,a=/CPU OS \d[_\d]*/gi,s=/Windows NT \d[\.\d]*/gi,o=/Linux x\d[_\d]*/gi,r=/Mac OS X \d[_|\.\d]*/gi;return/Android/gi.test(t)?e.test(t)?t.match(e)[0]+t.match(i)[0]:t.match(i)[0]:/iPhone/gi.test(t)?t.match(n)[0].replace(/CPU iPhone OS/gi,"iPhone").replace(/_/g,"."):/iPad/gi.test(t)?t.match(a)[0].replace(/CPU OS/gi,"iPad").replace(/_/g,"."):/Windows/gi.test(t)?"Windows "+(Math.min(parseInt(t.match(s)[0].replace(/Windows NT /,""))+1,10)<7?"XP":Math.min(parseInt(t.match(s)[0].replace(/Windows NT /,""))+1,10)):/Linux/gi.test(t)?t.match(o)[0]:/Macintosh/gi.test(t)?t.match(r)[0].replace(/_/g,"."):"unknown"}}},a296:function(t,e,i){"use strict";i("6c0c")},ade3:function(t,e,i){"use strict";i.d(e,"a",(function(){return o}));var n=i("53ca");function a(t,e){if("object"!==Object(n["a"])(t)||null===t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var a=i.call(t,e||"default");if("object"!==Object(n["a"])(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function s(t){var e=a(t,"string");return"symbol"===Object(n["a"])(e)?e:String(e)}function o(t,e,i){return e=s(e),e in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}},cad8:function(t,e,i){},dd7a:function(t,e,i){"use strict";i("cad8")},e445:function(t,e,i){"use strict";i.r(e);i("a481"),i("7f7f");var n=function(){var t=this,e=t._self._c;return e("div",{class:t.isMobilePhone?"":"pc-container"},[t.isMobilePhone?t._e():e("pcheader"),e("van-card",{staticStyle:{background:"white"},attrs:{thumb:t.guestInfo.avatar?t.guestInfo.avatar:"http://mpjoy.oss-cn-beijing.aliyuncs.com/20240917/d9301e81ab674112b3acf4a84aff4e96.png"}},[e("div",{staticStyle:{"font-size":"18px"},attrs:{slot:"title"},slot:"title"},[t._v(t._s(t.guestInfo.name))]),e("div",{staticStyle:{"padding-top":"10px","font-size":"14px",color:"grey"},attrs:{slot:"desc"},slot:"desc"},[t.guestInfo.unit?e("van-tag",{staticStyle:{margin:"5px 10px 5px 0px"},attrs:{size:"medium",round:"",type:"primary",plain:""}},[t._v(t._s(t.guestInfo.unit))]):t._e(),t.guestInfo.duties?e("van-tag",{staticStyle:{margin:"5px 10px 5px 0px"},attrs:{size:"medium",round:"",type:"warning",plain:""}},[t._v(t._s(t.guestInfo.duties))]):t._e()],1)]),e("van-collapse",{staticStyle:{"margin-top":"10px","padding-bottom":"100px"},model:{value:t.activeName,callback:function(e){t.activeName=e},expression:"activeName"}},[e("van-collapse-item",{attrs:{title:"嘉宾介绍",name:"1"}},[t.guestInfo.content?e("div",{staticClass:"content",staticStyle:{"white-space":"pre-wrap","word-wrap":"break-word","table-layout":"fixed","word-break":"break-all"},domProps:{innerHTML:t._s(t.guestInfo.content)}}):e("div",[t._v("暂无介绍")])]),t.topicSpeaker&&t.topicSpeaker.length>0?e("van-collapse-item",{staticClass:"transparent",attrs:{title:"负责主题主席任务",name:"2"}},t._l(t.topicSpeaker,(function(i){return e("van-card",{key:i.id,staticStyle:{background:"white","border-radius":"10px"}},[e("div",{staticStyle:{"font-size":"18px"},attrs:{slot:"title"},slot:"title"},[t._v(t._s(i.name))]),e("div",{staticStyle:{"padding-top":"10px","font-size":"14px",color:"grey"},attrs:{slot:"desc"},slot:"desc"},[t.isConfirm?e("div",{staticStyle:{display:"flex","align-items":"center","justify-content":"space-between"}},[e("div"),e("div",{staticStyle:{display:"flex","align-items":"center"}},[i.confirmStatus?e("div",{style:1==i.confirmStatus?"color: green":"color: red"},[t._v(t._s(1==i.confirmStatus?"已确认":"已拒绝"))]):t._e(),i.confirmStatus&&2!=i.confirmStatus?t._e():e("van-tag",{staticStyle:{"margin-left":"10px"},attrs:{size:"large",round:"",type:"primary"},on:{click:function(e){return t.confirm(1,i.flagId,"topicSpeaker")}}},[t._v("确认任务")]),i.confirmStatus&&1!=i.confirmStatus?t._e():e("van-tag",{staticStyle:{"margin-left":"10px"},attrs:{size:"large",round:"",type:"danger"},on:{click:function(e){return t.confirm(2,i.flagId,"topicSpeaker")}}},[t._v("拒绝任务")])],1)]):t._e(),e("div",[t._v("\n            "+t._s(i.startTime)+" ~\n            "+t._s(i.endTime.substring(11,19))+"\n          ")]),e("div",[t._v("会场："+t._s(i.placeName))])])])})),1):t._e(),t.topic&&t.topic.length>0?e("van-collapse-item",{staticClass:"transparent",attrs:{title:"负责主题主持任务",name:"3"}},t._l(t.topic,(function(i){return e("van-card",{key:i.id,staticStyle:{background:"white","border-radius":"10px"}},[e("div",{staticStyle:{"font-size":"18px"},attrs:{slot:"title"},slot:"title"},[t._v(t._s(i.name))]),e("div",{staticStyle:{"padding-top":"10px","font-size":"14px",color:"grey"},attrs:{slot:"desc"},slot:"desc"},[t.isConfirm?e("div",{staticStyle:{display:"flex","align-items":"center","justify-content":"space-between"}},[e("div"),e("div",{staticStyle:{display:"flex","align-items":"center"}},[i.confirmStatus?e("div",{style:1==i.confirmStatus?"color: green":"color: red"},[t._v(t._s(1==i.confirmStatus?"已确认":"已拒绝"))]):t._e(),i.confirmStatus&&2!=i.confirmStatus?t._e():e("van-tag",{staticStyle:{"margin-left":"10px"},attrs:{size:"large",round:"",type:"primary"},on:{click:function(e){return t.confirm(1,i.flagId,"topic")}}},[t._v("确认任务")]),i.confirmStatus&&1!=i.confirmStatus?t._e():e("van-tag",{staticStyle:{"margin-left":"10px"},attrs:{size:"large",round:"",type:"danger"},on:{click:function(e){return t.confirm(2,i.flagId,"topic")}}},[t._v("拒绝任务")])],1)]):t._e(),e("div",[t._v("\n            "+t._s(i.startTime)+" ~\n            "+t._s(i.endTime.substring(11,19))+"\n          ")]),e("div",[t._v("会场："+t._s(i.placeName))])])])})),1):t._e(),t.topicDiscuss&&t.topicDiscuss.length>0?e("van-collapse-item",{staticClass:"transparent",attrs:{title:"负责主题讨论任务",name:"3"}},t._l(t.topicDiscuss,(function(i){return e("van-card",{key:i.id,staticStyle:{background:"white","border-radius":"10px"}},[e("div",{staticStyle:{"font-size":"18px"},attrs:{slot:"title"},slot:"title"},[t._v(t._s(i.name))]),e("div",{staticStyle:{"padding-top":"10px","font-size":"14px",color:"grey"},attrs:{slot:"desc"},slot:"desc"},[t.isConfirm?e("div",{staticStyle:{display:"flex","align-items":"center","justify-content":"space-between"}},[e("div"),e("div",{staticStyle:{display:"flex","align-items":"center"}},[i.confirmStatus?e("div",{style:1==i.confirmStatus?"color: green":"color: red"},[t._v(t._s(1==i.confirmStatus?"已确认":"已拒绝"))]):t._e(),i.confirmStatus&&2!=i.confirmStatus?t._e():e("van-tag",{staticStyle:{"margin-left":"10px"},attrs:{size:"large",round:"",type:"primary"},on:{click:function(e){return t.confirm(1,i.flagId,"topicDiscuss")}}},[t._v("确认任务")]),i.confirmStatus&&1!=i.confirmStatus?t._e():e("van-tag",{staticStyle:{"margin-left":"10px"},attrs:{size:"large",round:"",type:"danger"},on:{click:function(e){return t.confirm(2,i.flagId,"topicDiscuss")}}},[t._v("拒绝任务")])],1)]):t._e(),e("div",[t._v("\n            "+t._s(i.startTime)+" ~\n            "+t._s(i.endTime.substring(11,19))+"\n          ")]),e("div",[t._v("会场："+t._s(i.placeName))])])])})),1):t._e(),t.schedule&&t.schedule.length>0?e("van-collapse-item",{staticClass:"transparent",attrs:{title:"负责讲课任务",name:"4"}},t._l(t.schedule,(function(i){return e("van-card",{key:i.id,staticStyle:{background:"white","border-radius":"10px"}},[e("div",{staticStyle:{"font-size":"18px"},attrs:{slot:"title"},slot:"title"},[t._v(t._s(i.name))]),e("div",{staticStyle:{"padding-top":"10px","font-size":"14px",color:"grey"},attrs:{slot:"desc"},slot:"desc"},[t.isConfirm?e("div",{staticStyle:{display:"flex","align-items":"center","justify-content":"space-between"}},[e("div"),e("div",{staticStyle:{display:"flex","align-items":"center"}},[i.confirmStatus?e("div",{style:1==i.confirmStatus?"color: green":"color: red"},[t._v(t._s(1==i.confirmStatus?"已确认":"已拒绝"))]):t._e(),i.confirmStatus&&2!=i.confirmStatus?t._e():e("van-tag",{staticStyle:{"margin-left":"10px"},attrs:{size:"large",round:"",type:"primary"},nativeOn:{click:function(e){return t.confirm(1,i.flagId,"schedule")}}},[t._v("确认任务")]),i.confirmStatus&&1!=i.confirmStatus?t._e():e("van-tag",{staticStyle:{"margin-left":"10px"},attrs:{size:"large",round:"",type:"danger"},nativeOn:{click:function(e){return t.confirm(2,i.flagId,"schedule")}}},[t._v("拒绝任务")])],1)]):t._e(),e("div",[t._v("\n            "+t._s(i.startTime)+" ~\n            "+t._s(i.endTime.substring(11,19))+"\n          ")]),e("div",[t._v("主题："+t._s(i.placeTopicName))]),e("div",[t._v("会场："+t._s(i.placeName))])])])})),1):t._e(),t.scheduleSpeaker&&t.scheduleSpeaker.length>0?e("van-collapse-item",{staticClass:"transparent",attrs:{title:"负责主持任务",name:"5"}},t._l(t.scheduleSpeaker,(function(i){return e("van-card",{key:i.id,staticStyle:{background:"white","border-radius":"10px"}},[e("div",{staticStyle:{"font-size":"18px"},attrs:{slot:"title"},slot:"title"},[t._v(t._s(i.name))]),e("div",{staticStyle:{"padding-top":"10px","font-size":"14px",color:"grey"},attrs:{slot:"desc"},slot:"desc"},[t.isConfirm?e("div",{staticStyle:{display:"flex","align-items":"center","justify-content":"space-between"}},[e("div"),e("div",{staticStyle:{display:"flex","align-items":"center"}},[i.confirmStatus?e("div",{style:1==i.confirmStatus?"color: green":"color: red"},[t._v(t._s(1==i.confirmStatus?"已确认":"已拒绝"))]):t._e(),i.confirmStatus&&2!=i.confirmStatus?t._e():e("van-tag",{staticStyle:{"margin-left":"10px"},attrs:{size:"large",round:"",type:"primary"},on:{click:function(e){return t.confirm(1,i.flagId,"scheduleSpeaker")}}},[t._v("确认任务")]),i.confirmStatus&&1!=i.confirmStatus?t._e():e("van-tag",{staticStyle:{"margin-left":"10px"},attrs:{size:"large",round:"",type:"danger"},on:{click:function(e){return t.confirm(2,i.flagId,"scheduleSpeaker")}}},[t._v("拒绝任务")])],1)]):t._e(),e("div",[t._v("\n            "+t._s(i.startTime)+" ~\n            "+t._s(i.endTime.substring(11,19))+"\n          ")]),e("div",[t._v("主题："+t._s(i.placeTopicName))]),e("div",[t._v("会场："+t._s(i.placeName))])])])})),1):t._e(),t.scheduleDiscuss&&t.scheduleDiscuss.length>0?e("van-collapse-item",{staticClass:"transparent",attrs:{title:"负责讲课讨论任务",name:"6"}},t._l(t.scheduleDiscuss,(function(i){return e("van-card",{key:i.id,staticStyle:{background:"white","border-radius":"10px"}},[e("div",{staticStyle:{"font-size":"18px"},attrs:{slot:"title"},slot:"title"},[t._v(t._s(i.name))]),e("div",{staticStyle:{"padding-top":"10px","font-size":"14px",color:"grey"},attrs:{slot:"desc"},slot:"desc"},[t.isConfirm?e("div",{staticStyle:{display:"flex","align-items":"center","justify-content":"space-between"}},[e("div"),e("div",{staticStyle:{display:"flex","align-items":"center"}},[i.confirmStatus?e("div",{style:1==i.confirmStatus?"color: green":"color: red"},[t._v(t._s(1==i.confirmStatus?"已确认":"已拒绝"))]):t._e(),i.confirmStatus&&2!=i.confirmStatus?t._e():e("van-tag",{staticStyle:{"margin-left":"10px"},attrs:{size:"large",round:"",type:"primary"},on:{click:function(e){return t.confirm(1,i.flagId,"scheduleDiscuss")}}},[t._v("确认任务")]),i.confirmStatus&&1!=i.confirmStatus?t._e():e("van-tag",{staticStyle:{"margin-left":"10px"},attrs:{size:"large",round:"",type:"danger"},on:{click:function(e){return t.confirm(2,i.flagId,"scheduleDiscuss")}}},[t._v("拒绝任务")])],1)]):t._e(),e("div",[t._v("\n            "+t._s(i.startTime)+" ~\n            "+t._s(i.endTime.substring(11,19))+"\n          ")]),e("div",[t._v("主题："+t._s(i.placeTopicName))]),e("div",[t._v("会场："+t._s(i.placeName))])])])})),1):t._e()],1),t.isConfirm?e("div",{staticClass:"fixed-bottom"},[t.guestInfo.isSchedule?t._e():e("div",{staticClass:"notice-bar"},[t._v("\n      如果任务时间您已经知悉并确认可以到场完成任务，请点击确认按钮。\n    ")]),e("div",{staticClass:"bottom-buttons"},[t.guestInfo.isSchedule?t._e():e("van-button",{staticClass:"submit-btn",attrs:{round:"",block:"",type:"info",loading:t.loading,"loading-text":"提交中"},nativeOn:{click:function(e){return t.confirm(1,t.id,"all")}}},[t._v("一键确认")]),e("van-button",{staticClass:"back-btn",attrs:{round:"",block:"",type:"primary"},on:{click:function(e){return t.$router.replace({path:"/schedules/expertIndex",query:{detailId:t.id}})}}},[t._v("返回上一页面")])],1)]):t._e(),e("img",{staticClass:"back",attrs:{src:t.activityInfo.backImg,alt:""},on:{click:t.cmsTurnBack}}),e("van-popup",{style:{width:"90%",maxWidth:"400px"},attrs:{position:"center"},model:{value:t.areaShow,callback:function(e){t.areaShow=e},expression:"areaShow"}},[e("van-cell-group",[e("van-field",{attrs:{name:"radio",label:"填写方式"},scopedSlots:t._u([{key:"input",fn:function(){return[e("van-radio-group",{attrs:{direction:"horizontal"},model:{value:t.confirmForm.status,callback:function(e){t.$set(t.confirmForm,"status",e)},expression:"confirmForm.status"}},[e("van-radio",{key:1,attrs:{name:1}},[t._v("确认无误")]),e("van-radio",{key:2,attrs:{name:2}},[t._v("有异议")])],1)]},proxy:!0}])}),e("div",{directives:[{name:"show",rawName:"v-show",value:1==t.confirmForm.status&&"all"!==t.confirmForm.method,expression:"confirmForm.status == 1 && confirmForm.method !== 'all'"}]},[e("van-field",{staticClass:"topic-name-field",attrs:{name:"课题名称",label:"课题名称",placeholder:"请确认课题名称",required:"",rules:[{required:!0,message:"请确认课题名称"}]},scopedSlots:t._u([{key:"input",fn:function(){return[e("textarea",{directives:[{name:"model",rawName:"v-model",value:t.confirmForm.topicName,expression:"confirmForm.topicName"}],staticClass:"topic-input",attrs:{rows:"3",placeholder:"请确认课题名称"},domProps:{value:t.confirmForm.topicName},on:{input:function(e){e.target.composing||t.$set(t.confirmForm,"topicName",e.target.value)}}})]},proxy:!0}]),model:{value:t.confirmForm.topicName,callback:function(e){t.$set(t.confirmForm,"topicName",e)},expression:"confirmForm.topicName"}})],1),e("div",{directives:[{name:"show",rawName:"v-show",value:2==t.confirmForm.status,expression:"confirmForm.status == 2"}]},[e("van-field",{attrs:{name:"reasonType",label:"异议原因"},scopedSlots:t._u([{key:"input",fn:function(){return[e("van-radio-group",{attrs:{direction:"vertical"},model:{value:t.confirmForm.reasonType,callback:function(e){t.$set(t.confirmForm,"reasonType",e)},expression:"confirmForm.reasonType"}},[e("van-radio",{key:1,attrs:{name:1}},[t._v("其他会议时间冲突，无法到场")]),e("van-radio",{key:2,attrs:{name:2}},[t._v("家中有事，无法到场")]),e("van-radio",{key:3,attrs:{name:3}},[t._v("自定义原因")])],1)]},proxy:!0}])}),e("van-field",{directives:[{name:"show",rawName:"v-show",value:3==t.confirmForm.reasonType,expression:"confirmForm.reasonType == 3"}],attrs:{name:"自定义原因",label:"自定义原因",placeholder:"请输入异议原因",required:"",rules:[{required:!0,message:"请输入异议原因"}]},model:{value:t.confirmForm.customReason,callback:function(e){t.$set(t.confirmForm,"customReason",e)},expression:"confirmForm.customReason"}})],1)],1),e("div",{staticStyle:{margin:"16px",display:"flex",gap:"10px"}},[e("van-button",{attrs:{round:"",block:"",type:"info"},on:{click:function(e){t.areaShow=!1}}},[t._v("取消")]),e("van-button",{attrs:{round:"",color:"#DD5C5F",block:"",type:"info",loading:t.loading,"loading-text":"提交中"},on:{click:t.confirmDo}},[t._v("提交")])],1)],1)],1)},a=[],s=(i("28a5"),i("6762"),i("2fdb"),i("7514"),i("66c7")),o=i("cacf"),r=i("1b69"),c={components:{pcheader:r["default"]},data:function(){return{areaShow:!1,confirmForm:{status:1,reason:"",reasonType:1,customReason:"",flagId:"",method:"",topicName:""},isConfirm:0,loading:!1,isMobilePhone:Object(o["c"])(),openid:void 0,activeName:["2","3","4","5","6"],activityId:void 0,id:void 0,guestInfo:{},schedule:[],scheduleDiscuss:[],scheduleSpeaker:[],topic:[],topicSpeaker:[],topicDiscuss:[],activityInfo:{}}},watch:{"confirmForm.status":function(t,e){t!==e&&this.$nextTick((function(){console.log("状态已切换:",t)}))}},mounted:function(){this.id=this.$route.query.detailId,this.isConfirm=this.$route.query.c||0,this.openid=this.$cookie.get("openid"),this.getActivityList(),this.getTopicAndSchedule()},methods:{confirm:function(t,e,i){var n=this;this.areaShow=!0,this.$nextTick((function(){n.confirmForm.flagId=e,n.confirmForm.status=t,n.confirmForm.method=i,n.confirmForm.reason="",n.confirmForm.reasonType=1,n.confirmForm.customReason="";var a="";if("topicSpeaker"===i||"topic"===i||"topicDiscuss"===i){var s="topicSpeaker"===i?n.topicSpeaker:"topic"===i?n.topic:n.topicDiscuss,o=s.find((function(t){return t.flagId===e}));o&&(a=o.name||"")}else if("schedule"===i||"scheduleSpeaker"===i||"scheduleDiscuss"===i){var r="schedule"===i?n.schedule:"scheduleSpeaker"===i?n.scheduleSpeaker:n.scheduleDiscuss,c=r.find((function(t){return t.flagId===e}));c&&(a=c.name||"")}n.confirmForm.topicName=a}))},confirmDo:function(){var t=this;if(this.loading=!0,2==this.confirmForm.status){if(1==this.confirmForm.reasonType?this.confirmForm.reason="其他会议时间冲突，无法到场":2==this.confirmForm.reasonType?this.confirmForm.reason="家中有事，无法到场":3==this.confirmForm.reasonType&&(this.confirmForm.reason=this.confirmForm.customReason),3==this.confirmForm.reasonType&&!this.confirmForm.customReason)return vant.Toast("请填写自定义异议原因"),void(this.loading=!1)}else if(1==this.confirmForm.status&&"all"!==this.confirmForm.method&&!this.confirmForm.topicName)return vant.Toast("请确认课题名称"),void(this.loading=!1);this.$fly.post("/pyp/web/activity/activityguest/confirm",this.confirmForm).then((function(e){t.loading=!1,e&&200===e.code?(vant.Toast("操作成功"),t.areaShow=!1,t.getTopicAndSchedule(),t.getActivityList()):vant.Toast(e.msg)}))},getActivityInfo:function(){var t=this;this.$fly.get("/pyp/activity/activity/info/".concat(this.activityId)).then((function(e){if(t.loading=!1,200==e.code){t.activityInfo=e.activity,t.activityInfo.backImg=t.activityInfo.backImg||"http://mpjoy.oss-cn-beijing.aliyuncs.com/20220614/9c9298f1d3474660977a02c8a84aafa8.png",document.title="嘉宾介绍-"+t.guestInfo.name;var i=s["a"].formatDate.format(new Date(t.activityInfo.startTime),"yyyy年MM月dd日"),n=s["a"].formatDate.format(new Date(t.activityInfo.endTime),"MM月dd日");if(i.includes(n)){var a="时间:"+i+"\n地址:"+t.activityInfo.address;t.$wxShare(t.guestInfo.name+"-嘉宾详情-"+t.activityInfo.name,t.activityInfo.shareUrl?t.activityInfo.shareUrl:t.activityInfo.mobileBanner.split(",")[0],a)}else{var o="时间:"+i+"-"+n+"\n地址:"+t.activityInfo.address;t.$wxShare(t.guestInfo.name+"-嘉宾详情-"+t.activityInfo.name,t.activityInfo.shareUrl?t.activityInfo.shareUrl:t.activityInfo.mobileBanner.split(",")[0],o)}}else vant.Toast(e.msg),t.activityInfo={}}))},getTopicAndSchedule:function(){var t=this;this.$fly.get("/pyp/web/activity/activityguest/getTopicAndSchedule/".concat(this.id)).then((function(e){t.loading=!1,200==e.code?(t.topic=e.result.topic,t.topicDiscuss=e.result.topicDiscuss,t.topicSpeaker=e.result.topicSpeaker,t.schedule=e.result.schedule,t.scheduleSpeaker=e.result.scheduleSpeaker,t.scheduleDiscuss=e.result.scheduleDiscuss):(vant.Toast(e.msg),t.activityInfo={})}))},getActivityList:function(){var t=this;this.$fly.get("/pyp/web/activity/activityguest/getById/".concat(this.id)).then((function(e){200==e.code?(t.guestInfo=e.result,t.activityId=e.result.activityId,t.getActivityInfo()):(vant.Toast(e.msg),t.guestInfo={})}))},cmsTurnBack:function(){this.$router.go(-1)},submit:function(){var t=this;this.guestInfo.isSchedule=1,this.guestInfo.isScheduleTime=s["a"].formatDate.format(new Date,"yyyy/MM/dd hh:mm:ss"),this.loading=!0,this.$fly.post("/pyp/web/activity/activityguest/updateInfo",this.guestInfo).then((function(e){t.loading=!1,e&&200===e.code?vant.Dialog.confirm({title:"更新成功",message:"点击确定，返回继续完善其他信息"}).then((function(){t.$router.replace({path:"/schedules/expertIndex",query:{detailId:t.id}})})).catch((function(){t.getActivityList()})):vant.Toast(e.msg)}))}}},l=c,u=(i("a296"),i("2877")),d=Object(u["a"])(l,n,a,!1,null,"4f2bb67f",null);e["default"]=d.exports}}]);
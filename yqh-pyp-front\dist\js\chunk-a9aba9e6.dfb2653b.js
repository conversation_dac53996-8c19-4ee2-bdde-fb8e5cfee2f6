(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-a9aba9e6"],{"11e9":function(t,e,i){var a=i("52a7"),n=i("4630"),s=i("6821"),r=i("6a99"),c=i("69a8"),o=i("c69a"),l=Object.getOwnPropertyDescriptor;e.f=i("9e1e")?l:function(t,e){if(t=s(t),e=r(e,!0),o)try{return l(t,e)}catch(i){}if(c(t,e))return n(!a.f.call(t,e),t[e])}},"17f1":function(t,e,i){"use strict";i("53dd")},"28a5":function(t,e,i){"use strict";var a=i("aae3"),n=i("cb7c"),s=i("ebd6"),r=i("0390"),c=i("9def"),o=i("5f1b"),l=i("520a"),u=i("79e5"),v=Math.min,d=[].push,p="split",h="length",f="lastIndex",y=4294967295,g=!u((function(){RegExp(y,"y")}));i("214f")("split",2,(function(t,e,i,u){var m;return m="c"=="abbc"[p](/(b)*/)[1]||4!="test"[p](/(?:)/,-1)[h]||2!="ab"[p](/(?:ab)*/)[h]||4!="."[p](/(.?)(.?)/)[h]||"."[p](/()()/)[h]>1||""[p](/.?/)[h]?function(t,e){var n=String(this);if(void 0===t&&0===e)return[];if(!a(t))return i.call(n,t,e);var s,r,c,o=[],u=(t.ignoreCase?"i":"")+(t.multiline?"m":"")+(t.unicode?"u":"")+(t.sticky?"y":""),v=0,p=void 0===e?y:e>>>0,g=new RegExp(t.source,u+"g");while(s=l.call(g,n)){if(r=g[f],r>v&&(o.push(n.slice(v,s.index)),s[h]>1&&s.index<n[h]&&d.apply(o,s.slice(1)),c=s[0][h],v=r,o[h]>=p))break;g[f]===s.index&&g[f]++}return v===n[h]?!c&&g.test("")||o.push(""):o.push(n.slice(v)),o[h]>p?o.slice(0,p):o}:"0"[p](void 0,0)[h]?function(t,e){return void 0===t&&0===e?[]:i.call(this,t,e)}:i,[function(i,a){var n=t(this),s=void 0==i?void 0:i[e];return void 0!==s?s.call(i,n,a):m.call(String(n),i,a)},function(t,e){var a=u(m,t,this,e,m!==i);if(a.done)return a.value;var l=n(t),d=String(this),p=s(l,RegExp),h=l.unicode,f=(l.ignoreCase?"i":"")+(l.multiline?"m":"")+(l.unicode?"u":"")+(g?"y":"g"),b=new p(g?l:"^(?:"+l.source+")",f),w=void 0===e?y:e>>>0;if(0===w)return[];if(0===d.length)return null===o(b,d)?[d]:[];var C=0,x=0,k=[];while(x<d.length){b.lastIndex=g?x:0;var A,_=o(b,g?d:d.slice(x));if(null===_||(A=v(c(b.lastIndex+(g?0:x)),d.length))===C)x=r(d,x,h);else{if(k.push(d.slice(C,x)),k.length===w)return k;for(var I=1;I<=_.length-1;I++)if(k.push(_[I]),k.length===w)return k;x=C=A}}return k.push(d.slice(C)),k}]}))},"53dd":function(t,e,i){},"5dbc":function(t,e,i){var a=i("d3f4"),n=i("8b97").set;t.exports=function(t,e,i){var s,r=e.constructor;return r!==i&&"function"==typeof r&&(s=r.prototype)!==i.prototype&&a(s)&&n&&n(t,s),t}},"88dd":function(t,e,i){},"8b97":function(t,e,i){var a=i("d3f4"),n=i("cb7c"),s=function(t,e){if(n(t),!a(e)&&null!==e)throw TypeError(e+": can't set as prototype!")};t.exports={set:Object.setPrototypeOf||("__proto__"in{}?function(t,e,a){try{a=i("9b43")(Function.call,i("11e9").f(Object.prototype,"__proto__").set,2),a(t,[]),e=!(t instanceof Array)}catch(n){e=!0}return function(t,i){return s(t,i),e?t.__proto__=i:a(t,i),t}}({},!1):void 0),check:s}},9093:function(t,e,i){var a=i("ce10"),n=i("e11e").concat("length","prototype");e.f=Object.getOwnPropertyNames||function(t){return a(t,n)}},9261:function(t,e,i){"use strict";i.r(e);i("28a5"),i("7f7f");var a,n=function(){var t=this,e=t._self._c;return e("div",{staticClass:"page-container"},[e("div",{staticClass:"page-header-bg"}),e("div",{staticClass:"activity-selector-wrapper"},[t.activityOptions.length>0?e("div",{staticClass:"activity-selector-simple"},[e("van-dropdown-menu",[e("van-dropdown-item",{attrs:{options:t.activityOptions},on:{change:t.onActivityChange,open:t.onDropdownOpen,close:t.onDropdownClose},model:{value:t.selectedActivityId,callback:function(e){t.selectedActivityId=e},expression:"selectedActivityId"}})],1)],1):t.userActivities.length>0?e("div",{staticClass:"activity-selector-fallback"},[e("van-cell-group",t._l(t.userActivities,(function(i){return e("van-cell",{key:i.id,class:{active:t.selectedActivityId===i.id},attrs:{title:i.name,"is-link":""},on:{click:function(e){return t.selectActivity(i)}}})})),1)],1):t.loading?t._e():e("div",{staticClass:"activity-selector empty"},[e("div",{staticClass:"empty-selector"},[e("van-icon",{staticClass:"selector-icon",attrs:{name:"apps-o"}}),e("span",{staticClass:"activity-title"},[t._v("暂无活动")])],1)]),t.loading?e("div",{staticClass:"loading-selector"},[e("van-loading",{attrs:{size:"20px"}},[t._v("加载中...")])],1):t._e()]),t.currentActivity?e("div",{staticClass:"notification-icon-wrapper"},[e("div",{staticClass:"notification-icon",on:{click:t.goToNotifications}},[e("van-icon",{attrs:{name:"chat-o",size:"20px"}}),t.unreadNotificationCount>0?e("van-badge",{attrs:{content:t.unreadNotificationCount,max:"99"}}):t._e()],1)]):t._e(),t.currentActivity&&t.currentActivityExpirationStatus?e("div",{staticClass:"expiration-status-wrapper"},[e("ActivityExpirationStatus",{attrs:{status:t.currentActivityExpirationStatus,"show-renewal-button":!0},on:{renew:t.showRenewalDialog}})],1):t._e(),t.currentActivity?e("div",{staticClass:"activity-info"},[e("div",{staticClass:"activity-banner"},[t.currentActivity.mobileBanner?e("van-image",{attrs:{width:"100%",height:"220px",src:t.currentActivity.mobileBanner.split(",")[0],fit:"cover",radius:"12px"}}):e("div",{staticClass:"default-banner"},[e("div",{staticClass:"banner-content"},[e("van-icon",{staticClass:"banner-icon",attrs:{name:"photo",size:"60"}}),e("p",{staticClass:"banner-title"},[t._v(t._s(t.currentActivity.name))]),e("div",{staticClass:"banner-decoration"})],1)])],1),e("div",{staticClass:"activity-details"},[e("div",{staticClass:"activity-header"},[e("h2",{staticClass:"activity-name"},[t._v(t._s(t.currentActivity.name))]),e("div",{staticStyle:{display:"flex",gap:"10px","justify-content":"flex-end"}},[e("van-button",{staticClass:"edit-button",attrs:{type:"primary",size:"small",icon:"edit",round:""},on:{click:t.editActivityDetail}},[t._v("\n          修改活动\n        ")]),e("van-button",{staticClass:"share-button",attrs:{type:"info",size:"small",icon:"share-o",round:""},on:{click:t.shareActivityDetail}},[t._v("\n          复制链接\n        ")]),e("van-button",{staticClass:"eye-button",attrs:{type:"info",size:"small",icon:"eye-o",round:""},on:{click:t.eyeActivity}},[t._v("\n          预览\n        ")])],1)])])]):t._e(),e("div",{staticClass:"operation-section"},[e("div",{staticClass:"section-header"},[e("h3",{staticClass:"section-title"},[e("van-icon",{staticClass:"title-icon",attrs:{name:"setting-o"}}),t._v("\n        活动管理\n      ")],1)]),e("div",{staticClass:"operation-grid"},[e("div",{staticClass:"operation-item",on:{click:t.manageFinishedVideos}},[e("div",{staticClass:"item-icon-wrapper video"},[e("van-icon",{attrs:{name:"video-o",size:"20"}})],1),e("span",{staticClass:"item-text"},[t._v("成品视频")]),e("div",{staticClass:"item-arrow"},[e("van-icon",{attrs:{name:"arrow",size:"12"}})],1)]),e("div",{staticClass:"operation-item",on:{click:t.manageVideos}},[e("div",{staticClass:"item-icon-wrapper video-alt"},[e("van-icon",{attrs:{name:"video",size:"20"}})],1),e("span",{staticClass:"item-text"},[t._v("素材视频")]),e("div",{staticClass:"item-arrow"},[e("van-icon",{attrs:{name:"arrow",size:"12"}})],1)]),e("div",{staticClass:"operation-item",on:{click:t.manageImages}},[e("div",{staticClass:"item-icon-wrapper image"},[e("van-icon",{attrs:{name:"photo-o",size:"20"}})],1),e("span",{staticClass:"item-text"},[t._v("图片素材")]),e("div",{staticClass:"item-arrow"},[e("van-icon",{attrs:{name:"arrow",size:"12"}})],1)]),e("div",{staticClass:"operation-item",on:{click:t.manageTexts}},[e("div",{staticClass:"item-icon-wrapper text"},[e("van-icon",{attrs:{name:"notes-o",size:"20"}})],1),e("span",{staticClass:"item-text"},[t._v("文案素材")]),e("div",{staticClass:"item-arrow"},[e("van-icon",{attrs:{name:"arrow",size:"12"}})],1)])])]),t.currentActivity||t.loading?t._e():e("div",{staticClass:"empty-state"},[e("div",{staticClass:"empty-content"},[e("van-icon",{staticClass:"empty-icon",attrs:{name:"photo-fail",size:"80"}}),e("h3",{staticClass:"empty-title"},[t._v("暂无活动数据")]),e("p",{staticClass:"empty-desc"},[t._v("点击去购买")]),e("van-button",{staticClass:"empty-button",attrs:{type:"primary"},on:{click:t.createActivity}},[e("van-icon",{attrs:{name:"plus"}}),t._v("\n        去购买\n      ")],1)],1)]),e("sub-modal",{attrs:{show:t.showFollowModal,qrcodeImgUrl:t.subUrl},on:{close:function(e){t.showFollowModal=!1}}}),t.userInfo.subscribe||1!=t.showSub||"wx0770d56458b33c67"!=t.appid?t.userInfo.subscribe||1!=t.showSub?t._e():e("img",{staticClass:"back",attrs:{src:"http://mpjoy.oss-cn-beijing.aliyuncs.com/20230609/a040f0b471a34b178e83d94ab937476d.png",alt:""},on:{click:function(e){t.showFollowModal=!0}}}):e("img",{staticClass:"back",attrs:{src:"http://mpjoy.oss-cn-beijing.aliyuncs.com/20230609/0b7689735164454a876666e00f2272d9.png",alt:""},on:{click:function(e){t.showFollowModal=!0}}}),e("div",{staticClass:"safe-area-bottom"}),e("ActivityRenewalDialog",{attrs:{"activity-id":t.selectedActivityId,"activity-name":t.currentActivity?t.currentActivity.name:"","expiration-status":t.currentActivityExpirationStatus,"renewal-packages":t.renewalPackages},on:{renewal:t.handleRenewal},model:{value:t.renewalDialogVisible,callback:function(e){t.renewalDialogVisible=e},expression:"renewalDialogVisible"}})],1)},s=[],r=i("ade3"),c=(i("6b54"),i("96cf"),i("1da1")),o=i("9694"),l=i("a6b9"),u={components:{ActivityExpirationStatus:o["a"],ActivityRenewalDialog:l["a"]},data:function(){return{loading:!1,userInfo:{},appid:"",renewalDialogVisible:!1,unreadNotificationCount:0}},computed:{userActivities:function(){return this.$store.state.activity.userActivities},selectedActivityId:function(){return this.$store.state.activity.selectedActivityId},currentActivity:function(){return this.$store.state.activity.currentActivity},activityOptions:function(){return this.$store.state.activity.activityOptions},currentActivityName:function(){return this.$store.getters["activity/currentActivityName"]},hasActivities:function(){return this.$store.getters["activity/hasActivities"]},currentActivityExpirationStatus:function(){return this.$store.getters["activity/currentActivityExpirationStatus"]},renewalPackages:function(){return this.$store.state.activity.renewalPackages},hasExpirationIssues:function(){return this.$store.getters["activity/expiredActivityCount"]>0||this.$store.getters["activity/expiringSoonActivityCount"]>0}},watch:{currentActivity:{handler:function(t,e){t&&e&&t.id!==e.id&&console.log("Index页面检测到活动变化:",t)},deep:!0},selectedActivityId:function(t,e){t!==e&&console.log("Index页面检测到选中活动ID变化:",t)}},mounted:function(){document.title="我的活动",this.appid=this.$cookie.get("appid"),this.initActivity()},activated:function(){console.log("Index页面被激活，检查活动状态同步"),this.checkActivitySync()},methods:(a={initActivity:function(){var t=Object(c["a"])(regeneratorRuntime.mark((function t(){var e;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return console.log("开始初始化活动数据..."),this.loading=!0,t.prev=2,t.next=5,this.$store.dispatch("activity/initializeActivity",{api:this.$fly,toast:this.$toast});case 5:if(e=t.sent,!e.success){t.next=14;break}return console.log("活动数据初始化成功:",e.data),t.next=10,this.loadExpirationStatus();case 10:return t.next=12,this.loadUnreadNotificationCount();case 12:t.next=15;break;case 14:console.error("活动数据初始化失败:",e.error);case 15:t.next=20;break;case 17:t.prev=17,t.t0=t["catch"](2),console.error("初始化活动数据时发生错误:",t.t0);case 20:return t.prev=20,this.loading=!1,t.finish(20);case 23:case"end":return t.stop()}}),t,this,[[2,17,20,23]])})));function e(){return t.apply(this,arguments)}return e}(),onActivityChange:function(){var t=Object(c["a"])(regeneratorRuntime.mark((function t(e){var i;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return console.log("Activity changed to:",e),t.prev=1,t.next=4,this.$store.dispatch("activity/switchActivity",e);case 4:i=t.sent,i.success?console.log("活动切换成功:",i.activity):(console.error("活动切换失败:",i.error),this.$toast.fail("切换活动失败")),t.next=12;break;case 8:t.prev=8,t.t0=t["catch"](1),console.error("切换活动时发生错误:",t.t0),this.$toast.fail("切换活动失败");case 12:case"end":return t.stop()}}),t,this,[[1,8]])})));function e(e){return t.apply(this,arguments)}return e}(),selectActivity:function(){var t=Object(c["a"])(regeneratorRuntime.mark((function t(e){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return console.log("Direct select activity:",e),t.next=3,this.onActivityChange(e.id);case 3:case"end":return t.stop()}}),t,this)})));function e(e){return t.apply(this,arguments)}return e}(),checkActivitySync:function(){this.hasActivities||(console.log("Index页面检测到没有活动数据，重新初始化"),this.initActivity())},onDropdownOpen:function(){console.log("Dropdown opened")},onDropdownClose:function(){console.log("Dropdown closed")},editActivity:function(){this.currentActivity?this.$router.push({name:"activityEdit",query:{id:this.currentActivity.id}}):this.$toast.fail("请先选择活动")},editActivityDetail:function(){this.currentActivity?this.$router.push({path:"/activity/edit",query:{id:this.currentActivity.id}}):this.$toast.fail("请先选择活动")},shareActivityDetail:function(){var t=this;if(this.currentActivity){var e="https://yqihua.com/p_front/#/cms/index?id="+this.currentActivity.id;navigator.clipboard?navigator.clipboard.writeText(e).then((function(){vant.Toast("已复制到剪贴板")})).catch((function(){t.fallbackCopyText(e)})):this.fallbackCopyText(e)}else this.$toast.fail("请先选择活动")},eyeActivity:function(){if(this.currentActivity){var t="https://yqihua.com/p_front/#/cms/index?id="+this.currentActivity.id;window.open(t)}else this.$toast.fail("请先选择活动")},manageFinishedVideos:function(){this.currentActivity?this.$router.push({path:"/materials/finished-video",query:{activityId:this.currentActivity.id}}):this.$toast.fail("请先选择活动")},manageVideos:function(){this.currentActivity?this.$router.push({name:"videoMaterials",query:{activityId:this.currentActivity.id}}):this.$toast.fail("请先选择活动")}},Object(r["a"])(a,"manageFinishedVideos",(function(){this.currentActivity?this.$router.push({name:"finishedVideo",query:{activityId:this.currentActivity.id}}):this.$toast.fail("请先选择活动")})),Object(r["a"])(a,"manageImages",(function(){this.currentActivity?this.$router.push({name:"imageMaterials",query:{activityId:this.currentActivity.id}}):this.$toast.fail("请先选择活动")})),Object(r["a"])(a,"manageTexts",(function(){this.currentActivity?this.$router.push({name:"textMaterials",query:{activityId:this.currentActivity.id}}):this.$toast.fail("请先选择活动")})),Object(r["a"])(a,"createActivity",(function(){this.$router.push({name:"salesmanScan"})})),Object(r["a"])(a,"formatNumber",(function(t){return t>=1e4?(t/1e4).toFixed(1)+"w":t>=1e3?(t/1e3).toFixed(1)+"k":t.toString()})),Object(r["a"])(a,"fallbackCopyText",(function(t){var e=document.createElement("textarea");e.value=t,document.body.appendChild(e),e.select();try{document.execCommand("copy"),vant.Toast("已复制到剪贴板")}catch(i){vant.Toast("复制失败")}document.body.removeChild(e)})),Object(r["a"])(a,"showRenewalDialog",(function(){this.currentActivity?(this.loadRenewalPackages(),this.renewalDialogVisible=!0):this.$toast.fail("请先选择活动")})),Object(r["a"])(a,"loadRenewalPackages",function(){var t=Object(c["a"])(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,this.$store.dispatch("activity/fetchRenewalPackages");case 3:t.next=9;break;case 5:t.prev=5,t.t0=t["catch"](0),console.error("获取续费套餐失败:",t.t0),this.$toast.fail("获取续费套餐失败");case 9:case"end":return t.stop()}}),t,this,[[0,5]])})));function e(){return t.apply(this,arguments)}return e}()),Object(r["a"])(a,"handleRenewal",function(){var t=Object(c["a"])(regeneratorRuntime.mark((function t(e){var i;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,this.$toast.loading({message:"创建订单中...",forbidClick:!0,duration:0}),t.next=4,this.$store.dispatch("activity/createRenewalOrder",e);case 4:i=t.sent,this.$toast.clear(),200===i.code?(this.renewalDialogVisible=!1,this.$toast.success("订单创建成功"),this.$router.push({path:"/payment",query:{orderSn:i.orderSn,amount:i.amount,type:"renewal"}})):this.$toast.fail(i.msg||"创建订单失败"),t.next=14;break;case 9:t.prev=9,t.t0=t["catch"](0),this.$toast.clear(),console.error("续费失败:",t.t0),this.$toast.fail(t.t0.message||"续费失败");case 14:case"end":return t.stop()}}),t,this,[[0,9]])})));function e(e){return t.apply(this,arguments)}return e}()),Object(r["a"])(a,"loadExpirationStatus",function(){var t=Object(c["a"])(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,this.$store.dispatch("activity/fetchUserActivitiesExpirationStatus");case 3:t.next=8;break;case 5:t.prev=5,t.t0=t["catch"](0),console.error("获取过期状态失败:",t.t0);case 8:case"end":return t.stop()}}),t,this,[[0,5]])})));function e(){return t.apply(this,arguments)}return e}()),Object(r["a"])(a,"loadUnreadNotificationCount",function(){var t=Object(c["a"])(regeneratorRuntime.mark((function t(){var e;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(this.currentActivity){t.next=2;break}return t.abrupt("return");case 2:return t.prev=2,t.next=5,this.$fly.get("/pyp/web/activity/activitynotify/noReadCount",{activityId:this.currentActivity.id});case 5:e=t.sent,200===e.code&&(this.unreadNotificationCount=e.result||0),t.next=12;break;case 9:t.prev=9,t.t0=t["catch"](2),console.error("获取未读通知数量失败:",t.t0);case 12:case"end":return t.stop()}}),t,this,[[2,9]])})));function e(){return t.apply(this,arguments)}return e}()),Object(r["a"])(a,"goToNotifications",(function(){this.$router.push("/notifications")})),a)},v=u,d=(i("17f1"),i("2877")),p=Object(d["a"])(v,n,s,!1,null,"66d161fb",null);e["default"]=p.exports},9694:function(t,e,i){"use strict";var a=function(){var t=this,e=t._self._c;return e("div",{staticClass:"expiration-status"},[t.status?e("div",{staticClass:"status-container"},[t.status.isExpired?e("div",{staticClass:"status-item expired"},[e("van-icon",{attrs:{name:"warning-o"}}),e("span",{staticClass:"status-text"},[t._v("活动已过期")]),e("span",{staticClass:"status-time"},[t._v(t._s(t.formatDate(t.status.expirationTime)))]),t.showRenewalButton?e("van-button",{attrs:{type:"danger",size:"mini"},on:{click:function(e){return t.$emit("renew")}}},[t._v("\n        立即续费\n      ")]):t._e()],1):t.status.isExpiringSoon?e("div",{staticClass:"status-item expiring-soon"},[e("van-icon",{attrs:{name:"clock-o"}}),e("span",{staticClass:"status-text"},[t._v("即将过期")]),e("span",{staticClass:"status-time"},[t._v("剩余"+t._s(t.status.remainingDays)+"天")]),t.showRenewalButton?e("van-button",{attrs:{type:"warning",size:"mini"},on:{click:function(e){return t.$emit("renew")}}},[t._v("\n        续费\n      ")]):t._e()],1):e("div",{staticClass:"status-item normal"},[e("van-icon",{attrs:{name:"success"}}),e("span",{staticClass:"status-text"},[t._v("正常使用中")]),t.status.expirationTime?e("span",{staticClass:"status-time"},[t._v("\n        "+t._s(t.status.remainingDays)+"天后过期\n      ")]):e("span",{staticClass:"status-time"},[t._v("永不过期")])],1)]):t.loading?e("div",{staticClass:"loading-container"},[e("van-loading",{attrs:{size:"16px"}}),e("span",[t._v("获取过期状态中...")])],1):e("div",{staticClass:"error-container"},[e("van-icon",{attrs:{name:"warning-o"}}),e("span",[t._v("无法获取过期状态")])],1)])},n=[],s={name:"ActivityExpirationStatus",props:{status:{type:Object,default:null},showRenewalButton:{type:Boolean,default:!0},loading:{type:Boolean,default:!1}},methods:{formatDate:function(t){if(!t)return"";var e=new Date(t);return e.toLocaleDateString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"})}}},r=s,c=(i("ee3e"),i("2877")),o=Object(c["a"])(r,a,n,!1,null,"7db95c6e",null);e["a"]=o.exports},a338:function(t,e,i){"use strict";i("ee19")},a6b9:function(t,e,i){"use strict";i("7f7f");var a=function(){var t=this,e=t._self._c;return e("van-popup",{style:{height:"70%"},attrs:{position:"bottom",round:"",closeable:""},on:{close:t.handleClose},model:{value:t.visible,callback:function(e){t.visible=e},expression:"visible"}},[e("div",{staticClass:"renewal-dialog"},[e("div",{staticClass:"dialog-header"},[e("h3",[t._v("活动续费")]),e("p",{staticClass:"activity-name"},[t._v(t._s(t.activityName))])]),e("div",{staticClass:"dialog-content"},[e("div",{staticClass:"current-status"},[e("h4",[t._v("当前状态")]),e("ActivityExpirationStatus",{attrs:{status:t.expirationStatus,"show-renewal-button":!1}})],1),e("div",{staticClass:"package-selection"},[e("h4",[t._v("选择续费套餐")]),e("div",{staticClass:"package-list"},t._l(t.renewalPackages,(function(i){return e("div",{key:i.id,staticClass:"package-item",class:{active:t.selectedPackageId===i.id},on:{click:function(e){return t.selectPackage(i)}}},[e("div",{staticClass:"package-info"},[e("div",{staticClass:"package-name"},[t._v(t._s(i.name))]),e("div",{staticClass:"package-desc"},[t._v(t._s(i.description))]),e("div",{staticClass:"package-days"},[t._v("延长 "+t._s(i.renewalDays)+" 天")])]),e("div",{staticClass:"package-price"},[e("span",{staticClass:"current-price"},[t._v("¥"+t._s(i.price))]),i.originalPrice>i.price?e("span",{staticClass:"original-price"},[t._v("\n                ¥"+t._s(i.originalPrice)+"\n              ")]):t._e()]),t.selectedPackageId===i.id?e("van-icon",{attrs:{name:"success",color:"#07c160"}}):t._e()],1)})),0)]),t.selectedPackage?e("div",{staticClass:"renewal-preview"},[e("h4",[t._v("续费后")]),e("div",{staticClass:"preview-info"},[e("div",{staticClass:"preview-item"},[e("span",[t._v("延长天数：")]),e("span",{staticClass:"highlight"},[t._v(t._s(t.selectedPackage.renewalDays)+" 天")])]),e("div",{staticClass:"preview-item"},[e("span",[t._v("新过期时间：")]),e("span",{staticClass:"highlight"},[t._v(t._s(t.getNewExpirationTime()))])]),e("div",{staticClass:"preview-item"},[e("span",[t._v("支付金额：")]),e("span",{staticClass:"price-highlight"},[t._v("¥"+t._s(t.selectedPackage.price))])])])]):t._e()]),e("div",{staticClass:"dialog-footer"},[e("van-button",{attrs:{block:"",type:"primary",disabled:!t.selectedPackageId,loading:t.submitting},on:{click:t.handleRenewal}},[t._v("\n        "+t._s(t.submitting?"处理中...":"立即支付 ¥".concat(t.selectedPackage?t.selectedPackage.price:0))+"\n      ")])],1)])])},n=[],s=(i("6b54"),i("96cf"),i("1da1")),r=(i("7514"),i("c5f6"),i("9694")),c={name:"ActivityRenewalDialog",components:{ActivityExpirationStatus:r["a"]},props:{value:{type:Boolean,default:!1},activityId:{type:[String,Number],default:null},activityName:{type:String,default:""},expirationStatus:{type:Object,default:null},renewalPackages:{type:Array,default:function(){return[]}}},data:function(){return{visible:this.value,selectedPackageId:null,submitting:!1}},computed:{selectedPackage:function(){var t=this;return this.renewalPackages.find((function(e){return e.id===t.selectedPackageId}))}},watch:{value:function(t){this.visible=t,t&&this.resetSelection()},visible:function(t){this.$emit("input",t)}},methods:{selectPackage:function(t){this.selectedPackageId=t.id},resetSelection:function(){this.selectedPackageId=null,this.submitting=!1},getNewExpirationTime:function(){if(!this.selectedPackage||!this.expirationStatus)return"";var t,e=this.expirationStatus.expirationTime,i=new Date;t=!e||new Date(e)<i?i:new Date(e);var a=new Date(t.getTime()+24*this.selectedPackage.renewalDays*60*60*1e3);return a.toLocaleDateString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"})},handleRenewal:function(){var t=Object(s["a"])(regeneratorRuntime.mark((function t(){var e;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(this.selectedPackageId&&this.activityId){t.next=2;break}return t.abrupt("return");case 2:this.submitting=!0;try{e={activityId:this.activityId,packageId:this.selectedPackageId,repeatToken:this.generateRepeatToken()},this.$emit("renewal",e)}catch(i){console.error("续费失败:",i),this.$toast.fail(i.message||"续费失败")}finally{this.submitting=!1}case 4:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),handleClose:function(){this.visible=!1},generateRepeatToken:function(){return Date.now()+"_"+Math.random().toString(36).substr(2,9)}}},o=c,l=(i("a338"),i("2877")),u=Object(l["a"])(o,a,n,!1,null,"cfdfe118",null);e["a"]=u.exports},aa77:function(t,e,i){var a=i("5ca1"),n=i("be13"),s=i("79e5"),r=i("fdef"),c="["+r+"]",o="​",l=RegExp("^"+c+c+"*"),u=RegExp(c+c+"*$"),v=function(t,e,i){var n={},c=s((function(){return!!r[t]()||o[t]()!=o})),l=n[t]=c?e(d):r[t];i&&(n[i]=l),a(a.P+a.F*c,"String",n)},d=v.trim=function(t,e){return t=String(n(t)),1&e&&(t=t.replace(l,"")),2&e&&(t=t.replace(u,"")),t};t.exports=v},ade3:function(t,e,i){"use strict";i.d(e,"a",(function(){return r}));var a=i("53ca");function n(t,e){if("object"!==Object(a["a"])(t)||null===t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var n=i.call(t,e||"default");if("object"!==Object(a["a"])(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function s(t){var e=n(t,"string");return"symbol"===Object(a["a"])(e)?e:String(e)}function r(t,e,i){return e=s(e),e in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}},c5f6:function(t,e,i){"use strict";var a=i("7726"),n=i("69a8"),s=i("2d95"),r=i("5dbc"),c=i("6a99"),o=i("79e5"),l=i("9093").f,u=i("11e9").f,v=i("86cc").f,d=i("aa77").trim,p="Number",h=a[p],f=h,y=h.prototype,g=s(i("2aeb")(y))==p,m="trim"in String.prototype,b=function(t){var e=c(t,!1);if("string"==typeof e&&e.length>2){e=m?e.trim():d(e,3);var i,a,n,s=e.charCodeAt(0);if(43===s||45===s){if(i=e.charCodeAt(2),88===i||120===i)return NaN}else if(48===s){switch(e.charCodeAt(1)){case 66:case 98:a=2,n=49;break;case 79:case 111:a=8,n=55;break;default:return+e}for(var r,o=e.slice(2),l=0,u=o.length;l<u;l++)if(r=o.charCodeAt(l),r<48||r>n)return NaN;return parseInt(o,a)}}return+e};if(!h(" 0o1")||!h("0b1")||h("+0x1")){h=function(t){var e=arguments.length<1?0:t,i=this;return i instanceof h&&(g?o((function(){y.valueOf.call(i)})):s(i)!=p)?r(new f(b(e)),i,h):b(e)};for(var w,C=i("9e1e")?l(f):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,isFinite,isInteger,isNaN,isSafeInteger,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,parseFloat,parseInt,isInteger".split(","),x=0;C.length>x;x++)n(f,w=C[x])&&!n(h,w)&&v(h,w,u(f,w));h.prototype=y,y.constructor=h,i("2aba")(a,p,h)}},ee19:function(t,e,i){},ee3e:function(t,e,i){"use strict";i("88dd")},fdef:function(t,e){t.exports="\t\n\v\f\r   ᠎             　\u2028\u2029\ufeff"}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-bd13b946","chunk-37a545c8"],{"1b69":function(t,e,i){"use strict";i.r(e);i("7f7f");var n,a=function(){var t=this,e=t._self._c;return e("div",{staticStyle:{background:"white","margin-bottom":"20px"}},[e("van-row",{attrs:{gutter:"20"}},[e("van-col",{attrs:{span:"16"}},[e("van-swipe",{staticStyle:{width:"100%"},attrs:{autoplay:3e3}},t._l(t.activityInfo.appFileList,(function(t,i){return e("van-swipe-item",{key:i},[e("van-image",{attrs:{width:"100%",src:t.url}})],1)})),1)],1),e("van-col",{attrs:{span:"8"}},[e("div",{staticStyle:{"margin-top":"20px"}},[e("van-cell",{attrs:{title:"会议名称",value:t.activityInfo.name}}),t.activityInfo.address?e("van-cell",{attrs:{title:"会议地点",value:t.activityInfo.address}}):t._e(),e("van-cell",{attrs:{title:"会议开始时间",value:t.activityInfo.startTime}}),e("van-cell",{attrs:{title:"会议结束时间",value:t.activityInfo.endTime}}),e("van-cell",{attrs:{title:"登录信息"},scopedSlots:t._u([{key:"default",fn:function(){return[t.userInfo?e("van-button",{attrs:{type:"primary",size:"small",round:""}},[t._v(t._s(t.userInfo.username)+"("+t._s(t.userInfo.mobile)+")")]):e("van-button",{attrs:{type:"info",size:"small",round:""},on:{click:t.showLogin}},[t._v("未登录(点击登录)")])]},proxy:!0}])}),"1736999159118508033"!=t.activityId?e("van-cell",{attrs:{title:"报名状态"},scopedSlots:t._u([{key:"default",fn:function(){return[1==t.isPay?e("van-button",{attrs:{type:"primary",size:"small",round:""},on:{click:t.turnApply}},[t._v("已报名")]):2==t.isPay?e("van-button",{attrs:{type:"warning",size:"small",round:""},on:{click:t.turnApply}},[t._v("已报名未交费")]):e("van-button",{attrs:{type:"info",size:"small",round:""},on:{click:t.turnApply}},[t._v("未报名")])]},proxy:!0}],null,!1,567203517)}):t._e()],1)])],1),e("van-tabs",{staticClass:"nav",on:{click:t.onClick},model:{value:t.cmsId,callback:function(e){t.cmsId=e},expression:"cmsId"}},t._l(t.cmsList,(function(t){return e("van-tab",{key:t.id,attrs:{title:t.title,name:t.id}})})),1),e("pclogin")],1)},s=[],o=i("ade3"),c=(i("a481"),i("6762"),i("2fdb"),i("cacf")),r=i("7dcb"),l=function(){var t=this,e=t._self._c;return e("van-dialog",{attrs:{title:"登录操作",width:"400px"},on:{confirm:t.login},model:{value:t.showPcLogin,callback:function(e){t.showPcLogin=e},expression:"showPcLogin"}},[e("div",{staticClass:"text-center padding"},[e("van-cell-group",{attrs:{inset:""}},[e("van-field",{attrs:{name:"手机号",label:"手机号",required:"",placeholder:"手机号",rules:[{required:!0,message:"请填写手机号"}]},model:{value:t.mobile,callback:function(e){t.mobile=e},expression:"mobile"}}),"1736999159118508033"!=t.activityId?e("van-field",{attrs:{center:"",clearable:"",maxlength:"6",label:"短信验证码",required:"",placeholder:"请输入短信验证码"},scopedSlots:t._u([{key:"button",fn:function(){return[e("van-button",{attrs:{size:"small",type:"primary",disabled:t.waiting},on:{click:function(e){return t.doSendSmsCode()}}},[t.waiting?e("span",[t._v(t._s(t.waitingTime)+"秒后重发")]):e("span",{staticStyle:{"font-size":"13px"}},[t._v("获取验证码")])])]},proxy:!0}],null,!1,850170227),model:{value:t.code,callback:function(e){t.code=e},expression:"code"}}):t._e()],1)],1)])},u=[],d={name:"pclogin",computed:{showPcLogin:{get:function(){return this.$store.state.user.showPcLogin},set:function(t){this.$store.commit("user/changePcLogin",t)}}},data:function(){return{activityId:void 0,waiting:!1,waitingTime:60,mobile:"",code:""}},mounted:function(){this.activityId=this.$route.query.id},methods:{login:function(){var t=this;return this.mobile?Object(c["b"])(this.mobile)?this.code||0x181b0f883e693000==this.activityId?/^\d{6}$/.test(this.code)||0x181b0f883e693000==this.activityId?void this.$fly.post("/pyp/web/user/pcLogin",{mobile:this.mobile,code:0x181b0f883e693000==this.activityId?"121212":this.code}).then((function(e){e&&200===e.code?(vant.Toast("登录成功"),t.$store.commit("user/update",e.userInfo),t.showPcLogin=!1,"applyIndex"!=t.$route.name&&"livesDetail"!=t.$route.name||location.reload()):vant.Toast(e.msg)})):(vant.Toast("验证码错误"),!1):(vant.Toast("请输入验证码"),!1):(vant.Toast("手机号格式错误"),!1):(vant.Toast("请输入手机号"),!1)},doSendSmsCode:function(){var t=this;return this.mobile?Object(c["b"])(this.mobile)?void this.$fly.post("/pyp/sms/sms/send",{mobile:this.mobile,activityId:this.activityId}).then((function(e){e&&200===e.code?(t.countdown(),vant.Toast("发送验证码成功")):vant.Toast(e.msg)})):(vant.Toast("手机号格式错误"),!1):(vant.Toast("请输入手机号"),!1)},countdown:function(){var t=this;this.waiting=!0;var e=window.setInterval((function(){t.waitingTime--,t.waitingTime<0&&(window.clearInterval(e),t.waitingTime=60,t.waiting=!1)}),1e3)}}},v=d,h=i("2877"),f=Object(h["a"])(v,l,u,!1,null,null,null),y=f.exports,p={components:{pclogin:y},data:function(){return{activityId:void 0,bannerIndex:0,cmsId:void 0,cmsList:[],cmsInfo:{},activityInfo:{}}},computed:{userInfo:{get:function(){return this.$store.state.user.userInfo},set:function(t){this.$store.commit("user/update",t)}},isPay:{get:function(){return this.$store.state.apply.isPay},set:function(t){this.$store.commit("apply/update",t)}}},mounted:function(){var t=(new Date).getTime();localStorage.setItem("logAddTime",t+36e5),this.openid=this.$cookie.get("openid"),this.activityId=this.$route.query.id,this.cmsId=sessionStorage.getItem("cmsId")||this.$route.query.cmsId,this.checkLogin(),this.getActivityInfo(),this.activityLogCount()},methods:(n={showLogin:function(){this.$store.commit("user/changePcLogin",!0)},checkLogin:function(){var t=this;this.$fly.get("/pyp/web/user/checkLogin").then((function(e){t.userInfo=e.result,t.userInfo?t.checkApply():t.isPay=0}))},bannerIndexChange:function(t){this.bannerIndex=t},checkApply:function(){var t=this;this.$fly.get("/pyp/web/activity/activityuserapplyorder/checkApply",{activityId:this.activityId}).then((function(e){200==e.code?(t.isPay=e.isPay,1==t.isPay||2==t.isPay&&vant.Dialog.alert({title:"提示",message:"您有一笔注册费待支付，点击跳转"}).then((function(){t.$router.push({name:"applySuccess",query:{orderId:e.result,id:t.activityId}})}))):vant.Toast(e.msg)}))},changeShowType:function(t){this.$emit("changeShowType",t)},turnApply:function(){var t=this.cmsList.filter((function(t){return t.model&&t.model.includes("applyIndex")}));if(t){var e=t[0];sessionStorage.setItem("cmsId",e.id);var i=e.model.replace("${activityId}",e.activityId);this.$router.push(JSON.parse(i))}else vant.Toast("暂未开启报名")},getActivityInfo:function(){var t=this;this.$fly.get("/pyp/activity/activity/info/".concat(this.activityId)).then((function(e){if(t.loading=!1,200==e.code){t.activityInfo=e.activity,document.title=t.activityInfo.name;var i=t.activityInfo.startTime,n=new Date(i.replace(/-/g,"/")),a=new Date,s=n.getTime()-a.getTime();t.dateCompare=s>0?s:0,t.getCmsList()}}))},activityLogCount:function(){var t=localStorage.getItem("logAddTime"),e=(new Date).getTime();e>t&&this.$fly.post("/pyp/activity/activityviewlog/count",{activityId:this.activityId,device:r["a"].getVersion()}).then((function(t){}))},getCmsList:function(){var t=this;this.$fly.get("/pyp/cms/cms/findByActivityId/".concat(this.activityId)).then((function(e){t.loading=!1,200==e.code?(t.cmsList=e.result,t.cmsId?t.changeShowType(t.cmsId):t.changeShowType(t.cmsList[0].id)):(vant.Toast(e.msg),t.cmsList=[])}))},onClick:function(t){if(console.log(t),sessionStorage.setItem("cmsId",t),this.cmsInfo=this.cmsList.filter((function(e){return e.id==t}))[0],this.cmsInfo.url&&Object(c["d"])(this.cmsInfo.url))location.href=this.cmsInfo.url;else if(this.cmsInfo.model&&this.isJSON(this.cmsInfo.model)){var e=this.cmsInfo.model.replace("${activityId}",this.cmsInfo.activityId);this.$router.push(JSON.parse(e))}else"cmsIndex"==this.$route.name?this.changeShowType(t):this.$router.push({name:"cmsIndex",query:{id:this.activityId,cmsId:t}})}},Object(o["a"])(n,"bannerIndexChange",(function(t){this.bannerIndex=t})),Object(o["a"])(n,"isJSON",(function(t){try{return JSON.parse(t),!0}catch(e){return!1}})),n)},m=p,g=(i("dd7a"),Object(h["a"])(m,a,s,!1,null,"7bd3d808",null));e["default"]=g.exports},"28a5":function(t,e,i){"use strict";var n=i("aae3"),a=i("cb7c"),s=i("ebd6"),o=i("0390"),c=i("9def"),r=i("5f1b"),l=i("520a"),u=i("79e5"),d=Math.min,v=[].push,h="split",f="length",y="lastIndex",p=4294967295,m=!u((function(){RegExp(p,"y")}));i("214f")("split",2,(function(t,e,i,u){var g;return g="c"=="abbc"[h](/(b)*/)[1]||4!="test"[h](/(?:)/,-1)[f]||2!="ab"[h](/(?:ab)*/)[f]||4!="."[h](/(.?)(.?)/)[f]||"."[h](/()()/)[f]>1||""[h](/.?/)[f]?function(t,e){var a=String(this);if(void 0===t&&0===e)return[];if(!n(t))return i.call(a,t,e);var s,o,c,r=[],u=(t.ignoreCase?"i":"")+(t.multiline?"m":"")+(t.unicode?"u":"")+(t.sticky?"y":""),d=0,h=void 0===e?p:e>>>0,m=new RegExp(t.source,u+"g");while(s=l.call(m,a)){if(o=m[y],o>d&&(r.push(a.slice(d,s.index)),s[f]>1&&s.index<a[f]&&v.apply(r,s.slice(1)),c=s[0][f],d=o,r[f]>=h))break;m[y]===s.index&&m[y]++}return d===a[f]?!c&&m.test("")||r.push(""):r.push(a.slice(d)),r[f]>h?r.slice(0,h):r}:"0"[h](void 0,0)[f]?function(t,e){return void 0===t&&0===e?[]:i.call(this,t,e)}:i,[function(i,n){var a=t(this),s=void 0==i?void 0:i[e];return void 0!==s?s.call(i,a,n):g.call(String(a),i,n)},function(t,e){var n=u(g,t,this,e,g!==i);if(n.done)return n.value;var l=a(t),v=String(this),h=s(l,RegExp),f=l.unicode,y=(l.ignoreCase?"i":"")+(l.multiline?"m":"")+(l.unicode?"u":"")+(m?"y":"g"),I=new h(m?l:"^(?:"+l.source+")",y),b=void 0===e?p:e>>>0;if(0===b)return[];if(0===v.length)return null===r(I,v)?[v]:[];var w=0,k=0,S=[];while(k<v.length){I.lastIndex=m?k:0;var x,L=r(I,m?v:v.slice(k));if(null===L||(x=d(c(I.lastIndex+(m?0:k)),v.length))===w)k=o(v,k,f);else{if(S.push(v.slice(w,k)),S.length===b)return S;for(var T=1;T<=L.length-1;T++)if(S.push(L[T]),S.length===b)return S;k=w=x}}return S.push(v.slice(w)),S}]}))},"66c7":function(t,e,i){"use strict";i("4917"),i("a481");var n=/([yMdhsm])(\1*)/g,a="yyyy-MM-dd";function s(t,e){e-=(t+"").length;for(var i=0;i<e;i++)t="0"+t;return t}e["a"]={formatDate:{format:function(t,e){return e=e||a,e.replace(n,(function(e){switch(e.charAt(0)){case"y":return s(t.getFullYear(),e.length);case"M":return s(t.getMonth()+1,e.length);case"d":return s(t.getDate(),e.length);case"w":return t.getDay()+1;case"h":return s(t.getHours(),e.length);case"m":return s(t.getMinutes(),e.length);case"s":return s(t.getSeconds(),e.length)}}))},parse:function(t,e){var i=e.match(n),a=t.match(/(\d)+/g);if(i.length==a.length){for(var s=new Date(1970,0,1),o=0;o<i.length;o++){var c=parseInt(a[o]),r=i[o];switch(r.charAt(0)){case"y":s.setFullYear(c);break;case"M":s.setMonth(c-1);break;case"d":s.setDate(c);break;case"h":s.setHours(c);break;case"m":s.setMinutes(c);break;case"s":s.setSeconds(c);break}}return s}return null},toWeek:function(t){var e=new Date(t).getDay(),i="";switch(e){case 0:i="s";break;case 1:i="m";break;case 2:i="t";break;case 3:i="w";break;case 4:i="t";break;case 5:i="f";break;case 6:i="s";break}return i}},toUserLook:function(t){var e=Math.floor(t/3600%24),i=Math.floor(t/60%60);return e<1?i+"分":e+"时"+i+"分"}}},"6bff":function(t,e,i){"use strict";i("b448")},"7dcb":function(t,e,i){"use strict";i("a481"),i("4917");e["a"]={getVersion:function(){var t=navigator.userAgent||window.navigator.userAgent,e=/HUAWEI|HONOR/gi,i=/[^;]+(?= Build)/gi,n=/CPU iPhone OS \d[_\d]*/gi,a=/CPU OS \d[_\d]*/gi,s=/Windows NT \d[\.\d]*/gi,o=/Linux x\d[_\d]*/gi,c=/Mac OS X \d[_|\.\d]*/gi;return/Android/gi.test(t)?e.test(t)?t.match(e)[0]+t.match(i)[0]:t.match(i)[0]:/iPhone/gi.test(t)?t.match(n)[0].replace(/CPU iPhone OS/gi,"iPhone").replace(/_/g,"."):/iPad/gi.test(t)?t.match(a)[0].replace(/CPU OS/gi,"iPad").replace(/_/g,"."):/Windows/gi.test(t)?"Windows "+(Math.min(parseInt(t.match(s)[0].replace(/Windows NT /,""))+1,10)<7?"XP":Math.min(parseInt(t.match(s)[0].replace(/Windows NT /,""))+1,10)):/Linux/gi.test(t)?t.match(o)[0]:/Macintosh/gi.test(t)?t.match(c)[0].replace(/_/g,"."):"unknown"}}},"84c1":function(t){t.exports=JSON.parse('[{"key":0,"value":"预告"},{"key":1,"value":"直播中"},{"key":2,"value":"回放中"}]')},ade3:function(t,e,i){"use strict";i.d(e,"a",(function(){return o}));var n=i("53ca");function a(t,e){if("object"!==Object(n["a"])(t)||null===t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var a=i.call(t,e||"default");if("object"!==Object(n["a"])(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function s(t){var e=a(t,"string");return"symbol"===Object(n["a"])(e)?e:String(e)}function o(t,e,i){return e=s(e),e in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}},b448:function(t,e,i){},cad8:function(t,e,i){},dd7a:function(t,e,i){"use strict";i("cad8")},f680:function(t,e,i){"use strict";i.r(e);i("7f7f");var n=function(){var t=this,e=t._self._c;return e("div",{class:t.isMobilePhone?"":"pc-container"},[t.isMobilePhone?t._e():e("pcheader"),t.dataList.length>0?e("div",t._l(t.dataList,(function(i){return e("van-card",{key:i.id,staticStyle:{background:"white","margin-top":"10px"},attrs:{thumb:i.cover?i.cover:"van-icon"},on:{click:function(e){return t.goLivesDetail(i.id)}}},[e("div",{staticStyle:{"font-size":"18px"},attrs:{slot:"title"},slot:"title"},[t._v(t._s(i.name))]),e("div",{staticStyle:{"padding-top":"10px","font-size":"14px",color:"grey"},attrs:{slot:"desc"},slot:"desc"},[e("van-tag",{staticStyle:{margin:"0 10px 5px 0px"},attrs:{size:"medium",round:"",type:"primary",plain:""}},[t._v(t._s(t._f("playStatusFilter")(i.playStatus)))]),2!=i.playStatus?e("div",[t._v("直播时间："+t._s(i.liveTime))]):t._e()],1)])})),1):t._e()],1)},a=[],s=(i("28a5"),i("6762"),i("2fdb"),i("66c7")),o=i("84c1"),c=i("cacf"),r=i("1b69"),l={components:{pcheader:r["default"]},data:function(){return{isMobilePhone:Object(c["c"])(),openid:void 0,activityId:void 0,playStatus:o,loading:!1,dataList:[]}},filters:{playStatusFilter:function(t){var e=o.filter((function(e){return e.key===t}));if(e.length>=1)return e[0].value}},mounted:function(){this.activityId=this.$route.query.id,this.openid=this.$cookie.get("openid"),this.getActivityInfo()},methods:{checkApply:function(){var t=this;this.$fly.get("/pyp/web/activity/activityuserapplyorder/checkApply",{activityId:this.activityId}).then((function(e){200==e.code?(t.isPay=e.isPay,t.$store.commit("apply/update",t.isPay),1==e.isPay&&1==e.verifyStatus?t.getActivityList():(sessionStorage.setItem("returnUrl",encodeURIComponent(window.location.href)),t.$router.push({name:"applyIndex",query:{id:t.activityId}}))):vant.Toast(e.msg)}))},getActivityInfo:function(){var t=this;this.$fly.get("/pyp/activity/activity/info/".concat(this.activityId)).then((function(e){if(t.loading=!1,200==e.code){t.activityInfo=e.activity,t.activityInfo.liveNeedApply?t.checkApply():t.getActivityList(),document.title=t.activityInfo.name+"直播列表";var i=s["a"].formatDate.format(new Date(t.activityInfo.startTime),"yyyy年MM月dd日"),n=s["a"].formatDate.format(new Date(t.activityInfo.endTime),"MM月dd日");if(i.includes(n)){var a="时间:"+i+"\n地址:"+t.activityInfo.address;t.$wxShare("正在直播-"+t.activityInfo.name,t.activityInfo.shareUrl?t.activityInfo.shareUrl:t.activityInfo.mobileBanner.split(",")[0],a)}else{var o="时间:"+i+"-"+n+"\n地址:"+t.activityInfo.address;t.$wxShare("正在直播-"+t.activityInfo.name,t.activityInfo.shareUrl?t.activityInfo.shareUrl:t.activityInfo.mobileBanner.split(",")[0],o)}}else vant.Toast(e.msg),t.activityInfo={}}))},getActivityList:function(){var t=this;this.$fly.get("/pyp/web/place/placeactivity/findByActivityIdLive/".concat(this.activityId)).then((function(e){t.loading=!1,200==e.code?(t.dataList=e.result,t.dataList&&1==t.dataList.length&&t.$router.push({name:"livesDetail",query:{detailId:t.dataList[0].id,id:t.activityId}})):(vant.Toast(e.msg),t.dataList=[])}))},goLivesDetail:function(t){this.$router.push({name:"livesDetail",query:{detailId:t,id:this.activityId}})}}},u=l,d=(i("6bff"),i("2877")),v=Object(d["a"])(u,n,a,!1,null,"30c40317",null);e["default"]=v.exports}}]);
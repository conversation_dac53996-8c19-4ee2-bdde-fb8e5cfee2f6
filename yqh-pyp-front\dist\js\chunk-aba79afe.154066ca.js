(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-aba79afe"],{"2e08":function(t,i,a){var e=a("9def"),s=a("9744"),n=a("be13");t.exports=function(t,i,a,o){var r=String(n(t)),c=r.length,l=void 0===a?" ":String(a),d=e(i);if(d<=c||""==l)return r;var u=d-c,p=s.call(l,Math.ceil(u/l.length));return p.length>u&&(p=p.slice(0,u)),o?p+r:r+p}},"5df3":function(t,i,a){"use strict";var e=a("02f4")(!0);a("01f9")(String,"String",(function(t){this._t=String(t),this._i=0}),(function(){var t,i=this._t,a=this._i;return a>=i.length?{value:void 0,done:!0}:(t=e(i,a),this._i+=t.length,{value:t,done:!1})}))},"8abc":function(t,i,a){},"8bee":function(t,i,a){"use strict";a.r(i);a("7f7f");var e=function(){var t=this,i=t._self._c;return i("div",{staticClass:"video-materials"},[i("div",{staticClass:"page-header-bg"}),i("van-nav-bar",{staticClass:"custom-nav-bar",attrs:{title:"素材视频","left-text":"返回","left-arrow":""},on:{"click-left":function(i){return t.$router.go(-1)}}}),i("div",{staticClass:"function-section"},[i("div",{staticClass:"section-content"},[t._m(0),i("div",{staticClass:"action-buttons"},[i("van-button",{staticClass:"upload-btn",attrs:{type:"primary",size:"large",icon:"plus"},on:{click:function(i){t.showUploadDialog=!0}}},[t._v("\n          上传素材视频\n        ")])],1),i("div",{staticClass:"search-filter-bar"},[i("van-search",{staticClass:"search-input",attrs:{placeholder:"搜索视频名称...",shape:"round"},on:{search:t.onSearch,clear:t.onSearch},model:{value:t.searchKeyword,callback:function(i){t.searchKeyword=i},expression:"searchKeyword"}}),i("van-button",{staticClass:"filter-btn",attrs:{icon:"filter-o"},on:{click:function(i){t.showFilter=!t.showFilter}}})],1)])]),i("div",{staticClass:"video-list"},[t.videoList.length>0?i("div",{staticClass:"stats-bar"},[i("div",{staticClass:"stats-info"},[i("span",{staticClass:"total-count"},[t._v("共 "+t._s(t.videoList.length)+" 个视频")])]),i("div",{staticClass:"list-actions"},[i("van-button",{attrs:{size:"mini",icon:"refresh",plain:""},on:{click:t.onSearch}},[t._v("刷新")])],1)]):t._e(),i("van-list",{staticClass:"custom-list",attrs:{finished:t.finished,"finished-text":"没有更多了"},on:{load:t.onLoad},model:{value:t.loading,callback:function(i){t.loading=i},expression:"loading"}},t._l(t.videoList,(function(a){return i("div",{key:a.id,staticClass:"video-card"},[i("div",{staticClass:"card-preview",on:{click:function(i){return t.previewVideo(a)}}},[i("div",{staticClass:"preview-container"},[a.mediaUrl?i("video",{staticClass:"preview-video",attrs:{src:a.mediaUrl,preload:"metadata",poster:a.coverUrl}}):i("div",{staticClass:"processing-state"},[i("van-loading",{attrs:{size:"20px",color:"#52c41a"}}),i("span",{staticClass:"processing-text"},[t._v("处理中")])],1),i("div",{staticClass:"play-overlay"},[i("div",{staticClass:"play-button"},[i("van-icon",{attrs:{name:"play",size:"20"}})],1)]),a.duration?i("div",{staticClass:"video-duration"},[t._v("\n              "+t._s(t.formatDuration(a.duration))+"\n            ")]):t._e(),i("div",{staticClass:"video-status"},[i("van-tag",{attrs:{size:"mini",type:"primary"}},[t._v("素材")])],1)])]),i("div",{staticClass:"card-content"},[i("div",{staticClass:"card-header"},[i("h4",{staticClass:"card-title"},[t._v(t._s(a.name))]),i("van-icon",{staticClass:"more-icon",attrs:{name:"more-o"},on:{click:function(i){return t.showVideoActions(a)}}})],1),i("div",{staticClass:"card-meta"},[i("div",{staticClass:"meta-row"},[i("span",{staticClass:"meta-item"},[i("van-icon",{attrs:{name:"description",size:"12"}}),t._v("\n                "+t._s(t.formatFileSize(a.fileSize))+"\n              ")],1),i("span",{staticClass:"meta-item"},[i("van-icon",{attrs:{name:"clock-o",size:"12"}}),t._v("\n                "+t._s(t.formatDuration(a.duration))+"\n              ")],1)]),i("div",{staticClass:"meta-row"},[i("span",{staticClass:"meta-item"},[i("van-icon",{attrs:{name:"eye-o",size:"12"}}),t._v("\n                "+t._s(a.useCount||0)+" 次使用\n              ")],1)])]),i("div",{staticClass:"card-actions"},[i("van-button",{attrs:{size:"mini",type:"primary",disabled:!a.mediaUrl,icon:"play-circle-o"},on:{click:function(i){return t.previewVideo(a)}}},[t._v("\n              预览\n            ")]),i("van-button",{attrs:{size:"mini",type:"danger",icon:"delete-o"},on:{click:function(i){return t.deleteVideo(a)}}},[t._v("\n              删除\n            ")])],1)])])})),0),t.loading||0!==t.videoList.length?t._e():i("div",{staticClass:"empty-state"},[i("div",{staticClass:"empty-content"},[i("van-icon",{staticClass:"empty-icon",attrs:{name:"video-o",size:"80"}}),i("h3",{staticClass:"empty-title"},[t._v("暂无素材视频")]),i("p",{staticClass:"empty-desc"},[t._v("上传您的第一个素材视频，开始创作之旅")]),i("div",{staticClass:"empty-actions"},[i("van-button",{attrs:{type:"primary",icon:"plus"},on:{click:function(i){t.showUploadDialog=!0}}},[t._v("\n            上传素材视频\n          ")])],1)],1)])],1),i("van-dialog",{staticClass:"custom-dialog upload-dialog",attrs:{title:"","show-cancel-button":"","confirm-button-loading":t.uploading,"confirm-button-text":"开始上传",width:"90%"},on:{confirm:t.confirmUpload},model:{value:t.showUploadDialog,callback:function(i){t.showUploadDialog=i},expression:"showUploadDialog"}},[i("div",{staticClass:"upload-form"},[i("div",{staticClass:"dialog-header"},[i("div",{staticClass:"header-icon upload-icon"},[i("van-icon",{attrs:{name:"plus",size:"32"}})],1),i("h3",{staticClass:"dialog-title"},[t._v("上传素材视频")]),i("p",{staticClass:"dialog-subtitle"},[t._v("批量上传您的视频素材文件")])]),i("van-field",{staticClass:"custom-field",attrs:{label:"批次名称",placeholder:"请输入批次名称（可选）"},model:{value:t.uploadForm.name,callback:function(i){t.$set(t.uploadForm,"name",i)},expression:"uploadForm.name"}}),i("div",{staticClass:"upload-section"},[i("div",{staticClass:"upload-header"},[i("span",{staticClass:"upload-title"},[t._v("选择视频文件")]),i("span",{staticClass:"upload-count"},[t._v(t._s(t.fileList.length)+"/5")])]),i("van-uploader",{staticClass:"custom-uploader",attrs:{"max-count":5,"after-read":t.afterRead,"before-delete":t.beforeDelete,accept:"video/*","max-size":209715200,multiple:"","preview-size":80,"upload-text":"选择视频"},on:{oversize:t.onOversize},model:{value:t.fileList,callback:function(i){t.fileList=i},expression:"fileList"}})],1),i("div",{staticClass:"upload-tips"},[i("div",{staticClass:"tip-header"},[i("van-icon",{attrs:{name:"info-o"}}),i("span",[t._v("上传须知")])],1),i("div",{staticClass:"tip-grid"},[i("div",{staticClass:"tip-item"},[i("van-icon",{staticClass:"tip-icon",attrs:{name:"video-o"}}),i("div",{staticClass:"tip-content"},[i("span",{staticClass:"tip-title"},[t._v("支持格式")]),i("span",{staticClass:"tip-desc"},[t._v("MP4、AVI、MOV、WMV、FLV")])])],1),i("div",{staticClass:"tip-item"},[i("van-icon",{staticClass:"tip-icon",attrs:{name:"description"}}),i("div",{staticClass:"tip-content"},[i("span",{staticClass:"tip-title"},[t._v("文件大小")]),i("span",{staticClass:"tip-desc"},[t._v("单个文件不超过200MB")])])],1),i("div",{staticClass:"tip-item"},[i("van-icon",{staticClass:"tip-icon",attrs:{name:"apps-o"}}),i("div",{staticClass:"tip-content"},[i("span",{staticClass:"tip-title"},[t._v("数量限制")]),i("span",{staticClass:"tip-desc"},[t._v("最多可选择5个文件")])])],1),i("div",{staticClass:"tip-item"},[i("van-icon",{staticClass:"tip-icon",attrs:{name:"star-o"}}),i("div",{staticClass:"tip-content"},[i("span",{staticClass:"tip-title"},[t._v("推荐内容")]),i("span",{staticClass:"tip-desc"},[t._v("高质量的视频素材")])])],1)])]),t.uploadProgress.length>0?i("div",{staticClass:"upload-progress"},[i("div",{staticClass:"progress-header"},[i("van-icon",{attrs:{name:"clock-o"}}),i("span",{staticClass:"progress-title"},[t._v("上传进度")])],1),t._l(t.uploadProgress,(function(a,e){return i("div",{key:e,staticClass:"progress-item"},[i("div",{staticClass:"progress-info"},[i("span",{staticClass:"progress-name"},[t._v(t._s(a.name))]),i("span",{staticClass:"progress-status"},[t._v(t._s(a.status))])]),i("van-progress",{attrs:{percentage:a.progress,color:a.color,"stroke-width":"6"}})],1)}))],2):t._e()],1)]),i("van-dialog",{staticClass:"custom-dialog preview-dialog",attrs:{title:"","show-confirm-button":!1,"show-cancel-button":"","cancel-button-text":"关闭",width:"95%"},model:{value:t.showPreviewDialog,callback:function(i){t.showPreviewDialog=i},expression:"showPreviewDialog"}},[t.previewVideoUrl?i("div",{staticClass:"preview-container"},[i("div",{staticClass:"preview-header"},[i("van-icon",{attrs:{name:"play-circle-o",size:"24"}}),i("span",{staticClass:"preview-title"},[t._v("素材视频预览")])],1),i("div",{staticClass:"video-wrapper"},[i("video",{staticClass:"preview-video",attrs:{src:t.previewVideoUrl,controls:"",autoplay:"",controlslist:"nodownload"}})])]):t._e()]),i("div",{staticClass:"safe-area-bottom"})],1)},s=[function(){var t=this,i=t._self._c;return i("div",{staticClass:"page-intro"},[i("h2",{staticClass:"page-title"},[t._v("素材视频管理")]),i("p",{staticClass:"page-desc"},[t._v("管理您的视频素材，支持批量上传和在线预览")])])}],n=(a("6b54"),a("f576"),a("5df3"),a("ac6a"),a("96cf"),a("1da1")),o={name:"VideoMaterials",data:function(){return{activityId:null,fileList:[],videoList:[],loading:!1,finished:!1,page:1,pageSize:10,searchKeyword:"",showUploadDialog:!1,showPreviewDialog:!1,previewVideoUrl:"",uploading:!1,showFilter:!1,viewMode:"grid",uploadForm:{name:""},uploadProgress:[]}},mounted:function(){var t=this.$route.query.activityId;if(t)this.activityId=t;else{var i=this.$store.state.activity.selectedActivityId;i&&(this.activityId=i)}if(!this.activityId)return this.$toast.fail("活动ID不能为空，请先选择活动"),void this.$router.push({name:"index"});this.loadVideoList()},methods:{onSearch:function(){this.page=1,this.videoList=[],this.finished=!1,this.loadVideoList()},onLoad:function(){this.loadVideoList()},toggleViewMode:function(){this.viewMode="grid"===this.viewMode?"list":"grid"},getTotalSize:function(){var t=this.videoList.reduce((function(t,i){return t+(i.fileSize||0)}),0);return this.formatFileSize(t)},loadVideoList:function(){var t=this;this.loading=!0;var i={page:this.page,limit:this.pageSize,activityId:this.activityId,type:0};this.searchKeyword&&(i.name=this.searchKeyword),this.$fly.get("/pyp/web/activity/activityvideo/list",i).then((function(i){if(t.loading=!1,200===i.code){var a=i.page.list||[];1===t.page?t.videoList=a:t.videoList=t.videoList.concat(a),t.page++,t.finished=t.videoList.length>=i.page.totalCount}else t.$toast.fail(i.msg||"获取视频列表失败"),t.finished=!0})).catch((function(){t.loading=!1,t.$toast.fail("获取视频列表失败"),t.finished=!0}))},afterRead:function(t){console.log("选择文件:",t)},beforeDelete:function(){var t=this;return new Promise((function(i){t.$dialog.confirm({title:"确认删除",message:"确定要删除这个文件吗？"}).then((function(){i(!0)})).catch((function(){i(!1)}))}))},onOversize:function(){this.$toast.fail("文件大小不能超过200MB")},confirmUpload:function(){var t=Object(n["a"])(regeneratorRuntime.mark((function t(){var i,a,e=this;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(0!==this.fileList.length){t.next=3;break}return this.$toast.fail("请选择视频文件"),t.abrupt("return");case 3:return this.uploading=!0,this.uploadProgress=[],t.prev=5,this.fileList.forEach((function(t,i){e.uploadProgress.push({name:t.file?t.file.name:"视频".concat(i+1),progress:0,status:"准备上传",color:"#1989fa"})})),i=this.fileList.map((function(t,i){return e.uploadSingleFile(t,i)})),t.next=10,Promise.all(i);case 10:return a=t.sent,t.next=13,this.saveUploadedVideos(a);case 13:this.$toast.success("成功上传 ".concat(a.length," 个视频")),this.showUploadDialog=!1,this.resetUploadForm(),this.onSearch(),t.next=23;break;case 19:t.prev=19,t.t0=t["catch"](5),console.error("上传失败:",t.t0),this.$toast.fail("上传失败，请重试");case 23:return t.prev=23,this.uploading=!1,t.finish(23);case 26:case"end":return t.stop()}}),t,this,[[5,19,23,26]])})));function i(){return t.apply(this,arguments)}return i}(),uploadSingleFile:function(){var t=Object(n["a"])(regeneratorRuntime.mark((function t(i,a){var e=this;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.abrupt("return",new Promise((function(t,s){var n=i.file,o=new FormData;e.uploadProgress[a].status="上传中",e.uploadProgress[a].progress=10,o.append("file",n),e.$fly.post("/pyp/web/upload",o).then((function(i){if(!i||200!==i.code)throw new Error(i.msg||"上传失败");e.uploadProgress[a].progress=100,e.uploadProgress[a].status="上传成功",e.uploadProgress[a].color="#52c41a",t({url:i.result,name:e.uploadForm.name||"视频".concat(a+1),fileSize:n.size,duration:0})})).catch((function(t){e.uploadProgress[a].progress=0,e.uploadProgress[a].status="上传失败",e.uploadProgress[a].color="#ff4444",s(t)}))})));case 1:case"end":return t.stop()}}),t)})));function i(i,a){return t.apply(this,arguments)}return i}(),saveUploadedVideos:function(){var t=Object(n["a"])(regeneratorRuntime.mark((function t(i){var a,e=this;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return a=i.map((function(t){var i={activityId:e.activityId,name:t.name,mediaUrl:t.url,fileSize:t.fileSize,duration:t.duration,type:0,useCount:0};return e.$fly.post("/pyp/web/activity/activityvideo/save",i)})),t.next=3,Promise.all(a);case 3:case"end":return t.stop()}}),t)})));function i(i){return t.apply(this,arguments)}return i}(),resetUploadForm:function(){this.uploadForm={name:""},this.fileList=[],this.uploadProgress=[]},previewVideo:function(t){t.mediaUrl?(this.previewVideoUrl=t.mediaUrl,this.showPreviewDialog=!0):this.$toast.fail("视频还在处理中，请稍后再试")},editVideo:function(t){this.$toast("编辑视频功能待开发"),console.log("编辑视频:",t)},showVideoActions:function(t){var i=this;this.$actionSheet({actions:[{name:"预览视频",callback:function(){return i.previewVideo(t)}},{name:"编辑视频",callback:function(){return i.editVideo(t)}},{name:"删除视频",color:"#ee0a24",callback:function(){return i.deleteVideo(t)}}]})},deleteVideo:function(t){var i=this;this.$dialog.confirm({title:"确认删除",message:"确定要删除这个视频吗？"}).then((function(){i.$fly.post("/pyp/web/activity/activityvideo/delete",[t.id]).then((function(t){200===t.code?(i.$toast.success("删除成功"),i.onSearch()):i.$toast.fail(t.msg||"删除失败")})).catch((function(){i.$toast.fail("删除失败")}))})).catch((function(){}))},getTypeName:function(t){var i={1:"成品视频",0:"素材视频"};return i[t]||"未知"},getTypeTag:function(t){var i={1:"success",0:"primary"};return i[t]||"default"},formatFileSize:function(t){if(!t)return"0 B";var i=["B","KB","MB","GB"],a=0;while(t>=1024&&a<i.length-1)t/=1024,a++;return"".concat(t.toFixed(1)," ").concat(i[a])},formatDuration:function(t){if(!t)return"00:00";var i=Math.floor(t/60),a=Math.floor(t%60);return"".concat(i.toString().padStart(2,"0"),":").concat(a.toString().padStart(2,"0"))}}},r=o,c=(a("ec1d"),a("2877")),l=Object(c["a"])(r,e,s,!1,null,"d94e7094",null);i["default"]=l.exports},9744:function(t,i,a){"use strict";var e=a("4588"),s=a("be13");t.exports=function(t){var i=String(s(this)),a="",n=e(t);if(n<0||n==1/0)throw RangeError("Count can't be negative");for(;n>0;(n>>>=1)&&(i+=i))1&n&&(a+=i);return a}},ac6a:function(t,i,a){for(var e=a("cadf"),s=a("0d58"),n=a("2aba"),o=a("7726"),r=a("32e9"),c=a("84f2"),l=a("2b4c"),d=l("iterator"),u=l("toStringTag"),p=c.Array,v={CSSRuleList:!0,CSSStyleDeclaration:!1,CSSValueList:!1,ClientRectList:!1,DOMRectList:!1,DOMStringList:!1,DOMTokenList:!0,DataTransferItemList:!1,FileList:!1,HTMLAllCollection:!1,HTMLCollection:!1,HTMLFormElement:!1,HTMLSelectElement:!1,MediaList:!0,MimeTypeArray:!1,NamedNodeMap:!1,NodeList:!0,PaintRequestList:!1,Plugin:!1,PluginArray:!1,SVGLengthList:!1,SVGNumberList:!1,SVGPathSegList:!1,SVGPointList:!1,SVGStringList:!1,SVGTransformList:!1,SourceBufferList:!1,StyleSheetList:!0,TextTrackCueList:!1,TextTrackList:!1,TouchList:!1},f=s(v),h=0;h<f.length;h++){var g,m=f[h],C=v[m],w=o[m],y=w&&w.prototype;if(y&&(y[d]||r(y,d,p),y[u]||r(y,u,m),c[m]=p,C))for(g in e)y[g]||n(y,g,e[g],!0)}},ec1d:function(t,i,a){"use strict";a("8abc")},f576:function(t,i,a){"use strict";var e=a("5ca1"),s=a("2e08"),n=a("a25f"),o=/Version\/10\.\d+(\.\d+)?( Mobile\/\w+)? Safari\//.test(n);e(e.P+e.F*o,"String",{padStart:function(t){return s(this,t,arguments.length>1?arguments[1]:void 0,!0)}})}}]);